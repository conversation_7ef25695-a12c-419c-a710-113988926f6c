<?php

/**
 * External service definitions for the accessreview block.
 *
 * @package block_mycoursetabs
 */

defined('MOODLE_INTERNAL') || die();

$functions = [
    'block_mycoursetabs_get_my_courses' => [
        'classname'     => 'block_mycoursetabs\external\my_courses_api',
        'methodname'    => 'get_my_courses',
        'description'   => 'Returns the list of courses the user is or can be enrolled in',
        'type'          => 'read',
        'ajax'          => true,
    ],

    'block_mycoursetabs_get_all_courses' => [
        'classname'     => 'block_mycoursetabs\external\all_courses_api',
        'methodname'    => 'get_all_courses',
        'description'   => 'Returns the list of courses that are offered for the users public',
        'type'          => 'read',
        'ajax'          => true,
    ],

    'block_mycoursetabs_get_filters' => [
        'classname'     => 'block_mycoursetabs\external\filters_api',
        'methodname'    => 'get_tab_filters',
        'description'   => 'Returns the list of selected tab filters',
        'type'          => 'read',
        'ajax'          => true,
    ],
];
