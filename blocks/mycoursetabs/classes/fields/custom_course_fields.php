<?php namespace block_mycoursetabs\fields;

abstract class custom_course_fields{

    /**
     * @label 'Formatos de Solução' 
     */
    const SOLUTION_FORMAT_SHORTNAME = 'solution_format';

    /**
     * @label 'Nível Ocupacional'  
     */
    const LEVEL_SHORTNAME = 'level';

    /**
     * @label 'Carga horária' 
     */
    const WORKLOAD_SHORTNAME = 'course_workload';

    /**
     * @label 'UF' 
     */
    const UF_SHORTNAME = 'course_uf';

    /**
     * @label 'Custo' 
     */
    const COST_SHORTNAME = 'custo';

    /**
     * @label 'Curso LearningFlix'
     */
    const LEARNINGFLIX_SHORTNAME = 'learningflix_course';

    /**
     * @label 'Mostrar nome do curso'
     */
    const DISPLAY_COURSENAME_SHORTNAME = 'display_coursename';

    public static function get_available_shortnames() : array {
        return [
            self::SOLUTION_FORMAT_SHORTNAME,
            self::LEVEL_SHORTNAME,
            self::UF_SHORTNAME,
            self::DISPLAY_COURSENAME_SHORTNAME
        ];
    }
}