<?php

namespace block_mycoursetabs\filters;

use \block_mycoursetabs\filters\abstract_filters;
use \block_mycoursetabs\filters\mycourses_filters;
use \block_mycoursetabs\filters\recommended_filters;
use \block_mycoursetabs\filters\popular_filters;
use \block_mycoursetabs\filters\favourite_filters;
use \block_mycoursetabs\filters\allcourses_filters;

abstract class tab_filters_factory
{

    public static function factory(string $tab, array|object $values = []): abstract_filters
    {
        switch ($tab) {
            case mycourses_filters::get_tab_name():
                return new mycourses_filters($values);
            case allcourses_filters::get_tab_name():
                return new allcourses_filters($values);
            default:
                throw new \Exception("Invalid tab name!");
        }
    }
}
