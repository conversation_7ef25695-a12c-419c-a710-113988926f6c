<?php

namespace block_mycoursetabs\filters;

use \tool_usercoursestatus\utils\constants as status_constants;
use local_courseblockapi\support\enrollment_status; // progress status
use local_courseblockapi\support\my_courses_sorters;
use local_courseblockapi\traits\search_trait;
use local_ssystem\constants\custom_course_fields;

require_once($CFG->dirroot . "/local/courseblockapi/lib.php");

abstract class abstract_filters
{

    const STATUS_FILTER = 'status';
    const STATUS_PROGRESS_FILTER = 'status_progress';
    const MOST_ACCESSED_FILTER = 'most_accessed';
    const SOLUTION_FORMAT_FILTER = 'solution_format';
    const LEVEL_FILTER = 'level';
    const UF_FILTER = 'uf';
    const CATEGORYID_FILTER = 'categoryid';
    const FAVOURITE_FILTER = 'favourite';
    const RECOMMENDED_FILTER = 'recommended';
    const SEARCH_FILTER = 'search';

    protected array $values = [];
    protected array $filters = [];

    public function __construct(array|object $values = [])
    {
        $this->values = (array) $values;
    }

    abstract public static function get_tab_name(): string;

    public function export(): object
    {
        return (object) [
            self::STATUS_FILTER => $this->make_status_filter(),
            self::STATUS_PROGRESS_FILTER => $this->make_status_progress_filter(),
            self::MOST_ACCESSED_FILTER => $this->make_most_accessed_filter(),
            self::SOLUTION_FORMAT_FILTER => $this->make_solution_format_filter(),
            self::LEVEL_FILTER => $this->make_level_filter(),
            self::UF_FILTER => $this->make_uf_filter(),
            self::CATEGORYID_FILTER => $this->make_categoryid_filter(),
            self::FAVOURITE_FILTER => $this->make_favourite_filter(),
            self::RECOMMENDED_FILTER => $this->make_recommended_filter(),
            self::SEARCH_FILTER => $this->make_search_filter(),
        ];
    }

    protected function get_current_value(string $key)
    {
        if (isset($this->values[$key])) {
            return $this->values[$key];
        }
        return null;
    }

    protected function make_status_filter(): object
    {
        $selected = $this->get_current_value(self::STATUS_FILTER);

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];

        foreach (status_constants::get_status() as $key => $value) {
            $filter->options[] = (object)[
                "name" => get_string($key, 'tool_usercoursestatus'),
                "value" => $value,
                "selected" => $value == $selected,
            ];
        }

        return $filter;
    }

    protected function make_status_progress_filter(): object
    {
        return (object)[
            'enabled' => false,
            'options' => [],
        ];
    }
    protected function make_categoryid_filter(): object
    {
        $selected = $this->get_current_value(self::CATEGORYID_FILTER);
        $category_list = \core_course_category::make_categories_list();

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];

        foreach ($category_list as $key => $value) {
            $filter->options[] = (object)[
                "name" => $value,
                "value" => $key,
                "selected" => $value == $selected,
            ];
        }

        return $filter;
    }

    protected function make_solution_format_filter(): object
    {
        $selected = $this->get_current_value(self::SOLUTION_FORMAT_FILTER);
        $options = custom_course_fields::get_solution_format_options();

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];

        foreach ($options as $option) {

            $filter->options[] = (object)[
                'name' => $option,
                'value' => $option,
                'selected' => $option === $selected
            ];
        };

        return $filter;
    }

    protected function make_level_filter(): object
    {
        $selected = $this->get_current_value(self::LEVEL_FILTER);
        $level_list = local_courseblockapi_get_nivel_ocupacional_list($selected);

        $filter = (object)[
            'enabled' => false,
            'options' => [],
        ];

        foreach ($level_list as $item) {
            $item["value"] = $item["name"];
            $filter->options[] = $item;
        };

        return $filter;
    }

    protected function make_uf_filter(): object
    {
        $selected = $this->get_current_value(self::UF_FILTER);
        $uf_list = local_courseblockapi_get_uf_list($selected);

        $filter = (object)[
            'enabled' => true,
            'options' => [],
        ];

        foreach ($uf_list as $item) {
            $item["value"] = $item["name"];
            $filter->options[] = $item;
        };

        return $filter;
    }


    protected function make_favourite_filter(): object
    {
        $current = $this->get_current_value(self::FAVOURITE_FILTER);

        $filter = (object)[
            'enabled' => true,
            'value' => 1,
            'checked' => (bool) $current,
        ];

        return $filter;
    }

    protected function make_recommended_filter(): object
    {
        $current = $this->get_current_value(self::RECOMMENDED_FILTER);

        $filter = (object)[
            'enabled' => true,
            'value' => 1,
            'checked' => (bool) $current,
        ];

        return $filter;
    }

    protected function make_most_accessed_filter(): object
    {
        $current = $this->get_current_value(self::MOST_ACCESSED_FILTER);

        $filter = (object)[
            'enabled' => true,
            'value' => 1,
            'checked' => (bool) $current,
        ];

        return $filter;
    }

    protected function make_search_filter(): string
    {
        $current = $this->get_current_value(self::SEARCH_FILTER);
        $filter = $current ?: "";
        return $filter;
    }
}
