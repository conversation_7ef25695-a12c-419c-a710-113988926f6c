<?php namespace block_mycoursetabs\external;

use \StdClass;
use \context_system;
use \core_external\external_api;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \core_external\external_single_structure;
use \core_external\external_value;
use \invalid_parameter_exception;
use \block_mycoursetabs\external\abstract_paginated_api;
use \block_mycoursetabs\external\exporter\course_card_exporter;
use tool_lfxp\util\query_object;
use \tool_usercoursestatus\utils\constants as status_constants;
use \local_offermanager\constants as offermanager_constants;
class all_courses_api extends abstract_paginated_api {

    protected static function define_filters_structure() : external_single_structure {
        return new external_single_structure([
            'categoryid'  => new external_value(PARAM_INT, 'Category ID', VALUE_DEFAULT, 0),
            'favourite'   => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            //'recommended' => new external_value(PARAM_BOOL, 'Only recommended course', VALUE_DEFAULT, false),
            'uf'   => new external_value(PARAM_TEXT, 'UF', VALUE_DEFAULT, 'all'),
            //'level'       => new external_value(PARAM_TEXT, 'Course level', VALUE_DEFAULT, 'all'),
            'solution_format'    => new external_value(PARAM_TEXT, 'Course solution format filter', VALUE_DEFAULT, 'all'),
            'search'      => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            //'status'  => new external_value(PARAM_INT, 'Enrolment status', VALUE_DEFAULT, -1),
            'most_accessed' => new external_value(PARAM_BOOL, 'Order by most accessed courses', VALUE_DEFAULT, false),
        ], VALUE_DEFAULT, []);
    }
    
    public static function get_all_courses_parameters(){
        return self::append_common_pagination_parameters([
            'filters' => self::define_filters_structure(),
        ]);
    }


    public static function get_all_courses(int $page = 1, int $per_page = 16, array $filters = []){
        global $OUTPUT, $USER, $PAGE, $DB;

        if ($page < 1) {
            $page = 1;
        }

        $courses = [];
        $params = compact('page', 'per_page', 'filters');
        $params = self::validate_parameters(self::get_all_courses_parameters(), $params);

        $context = context_system::instance();
        require_capability('moodle/category:viewcourselist', $context);
        $PAGE->set_context($context);

        $renderer = $PAGE->get_renderer('block_mycoursetabs');

        $query = self::build_query($params['filters']);
        $recordset = $query->get_recordset(($page - 1) * $per_page, $per_page);

        foreach ($recordset as $record) {
            $record->completed = in_array($record->statusid, [
                status_constants::STATUS_COMPLETED,
                status_constants::STATUS_APPROVED,
            ]);

            $record->is_enrolled = $record->statusid != status_constants::STATUS_NOTENROLED;

            $exporter = new course_card_exporter($record, ['userid' => (int) $USER->id]);
            $courses[] = $exporter->export($renderer);
        }

        $recordset->close();
        $total = $query->count();

        return [
            'page'        => $page,
            'per_page'    => $per_page,
            'total'       => $total,
            'total_pages' => ceil($total / $per_page),
            'show_all'    => $total > COUNT($courses),
            'items'     => $courses,
            'empty_url'   => !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '',
            'filters'     => $params['filters'],
            'query' => json_encode($query),
        ];
    }


    public static function get_all_courses_returns(){
        return self::append_common_pagination_returns([
            'items' => new external_multiple_structure(
                course_card_exporter::get_read_structure(VALUE_OPTIONAL),
                'List of courses',
                VALUE_DEFAULT,
                []
            ),
            'filters' => self::define_filters_structure(),
            'query'   => new external_value(PARAM_RAW, 'Debug query info', VALUE_OPTIONAL),
        ]);
    }


    protected static function build_query($filters): query_object {
        global $USER, $DB;

        $user_audience_ids = $DB->get_fieldset_sql(
            'SELECT audienceid FROM {local_audience_members} WHERE userid = ?',
            [$USER->id]
        );

        if (empty($user_audience_ids)) {
            $query = new query_object();
            $query->from = '{course}';
            $query->add_where_condition('1 = 0');
            return $query;
        }

        list($audience_in_sql, $audience_in_params) = $DB->get_in_or_equal($user_audience_ids, SQL_PARAMS_NAMED, 'audienceid');

        $query = new query_object();

        $subquery_sql = "
            SELECT DISTINCT c.courseid
            FROM {local_offermanager_audience} a
            JOIN {local_offermanager_course} c ON c.offerid = a.offerid
            JOIN {local_offermanager} o ON o.id = a.offerid
            WHERE a.audienceid " . $audience_in_sql . " AND o.status = " . offermanager_constants::OFFER_STATUS_ACTIVE;

        $query->fields = "
            mc.id, {$USER->id} AS userid, mc.fullname, mcc.id AS categoryid, mcc.name AS category,
            mf.id IS NOT NULL AS is_favorite, COALESCE(mcmv.view_counter, 0) AS access_count,
            lou.situation, lou.progress";

        $query->from = "
            {course} mc
            JOIN {course_categories} mcc ON (mcc.id = mc.category AND mcc.visible = 1)
            LEFT JOIN {local_custom_fields_course} mlcfc ON (mlcfc.courseid = mc.id)
            LEFT JOIN {favourite} mf ON (mf.itemid = mc.id AND mf.userid = :userid_fav AND mf.itemtype = 'courses')
            LEFT JOIN {courseviews_monthly_views} mcmv ON (mcmv.courseid = mc.id AND mcmv.year = :current_year AND mcmv.month = :current_month)
            LEFT JOIN {local_offermanager_ue} lou ON (lou.courseid = mc.id AND lou.userid = :userid)";

        $query->add_where_condition("mc.id IN ({$subquery_sql})");
        $query->add_where_condition("mc.visible = 1");

        $params = $audience_in_params;

        $params['userid'] = $USER->id;
        $params['userid_fav'] = $USER->id;
        $params['current_year'] = date('Y');
        $params['current_month'] = date('n');

        if (!empty($filters['favourite'])) {
            $query->add_where_condition('mf.id IS NOT NULL');
        }
        if (!empty($filters['categoryid'])) {
            $catid = $filters['categoryid'];
            $like_path_sql = $DB->sql_like('mcc.path', ':catpath');
            $eq_id_sql = 'mcc.id = :catid';
            $query->add_where_condition("($like_path_sql OR $eq_id_sql)");
            $params['catpath'] = '%/' . $catid . '/%';
            $params['catid'] = $catid;
        }
        if (!empty($filters['uf']) && $filters['uf'] !== 'all') {
            $ufcondition = $DB->sql_like('mlcfc.course_uf', ':uf', false);
            $query->add_where_condition($ufcondition);
            $params['uf'] = '%' . trim($filters['uf']) . '%';
        }
        if (!empty($filters['solution_format']) && $filters['solution_format'] !== 'all') {
            $query->add_where_condition($DB->sql_like('mlcfc.solution_format', ':solution_format'));
            $params['solution_format'] = '%' . trim($filters['solution_format']) . '%';
        }
        if (!empty($filters['search'])) {
            $searchcondition = $DB->sql_like('mc.fullname', ':searchtext', false);
            $query->add_where_condition($searchcondition);
            $params['searchtext'] = '%' . trim($filters['search']) . '%';
        }

        $query->params = $params;

        // Ordenação.pelos mais acessados(filtro)
        if (!empty($filters['most_accessed'])) {
            $query->order = 'access_count DESC, mc.fullname ASC';
        } else {
            $query->order = 'mc.fullname ASC';
        }
        
        return $query;
    }

    
}