<?php

namespace block_mycoursetabs\external;

use \core_external\external_api;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \core_external\external_single_structure;
use \core_external\external_value;
use \invalid_parameter_exception;

use \block_mycoursetabs\filters\tab_filters_factory;

// require_once($CFG->dirroot."/blocks/mycoursetabs/lib.php");

class filters_api extends external_api
{

    const TAB_MYCOURSES = 'mycourses';
    const TAB_ALLCOURSES = 'allcourses';
    const TAB_RECOMMENDED = 'recommended';
    const TAB_FAVOURITES = 'favourite';
    const TAB_POPULAR = 'popular';

    protected static function define_filters_structure(): external_single_structure
    {
        return new external_single_structure([
            'categoryid'      => new external_value(PARAM_INT, 'Category ID', VALUE_DEFAULT, 0),
            'favourite'       => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            'recommended'     => new external_value(PARAM_BOOL, 'Only recommended course', VALUE_DEFAULT, false),
            'most_accessed'   => new external_value(PARAM_BOOL, 'Order by most accessed courses', VALUE_DEFAULT, false),
            'uf'              => new external_value(PARAM_TEXT, 'Course UF', VALUE_DEFAULT, 'all'),
            'level'           => new external_value(PARAM_TEXT, 'Course level', VALUE_DEFAULT, 'all'),
            'solution_format' => new external_value(PARAM_TEXT, 'Course solution format filter', VALUE_DEFAULT, 'all'),
            'search'          => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            'status'          => new external_value(PARAM_INT, 'Enrolment status', VALUE_DEFAULT, -1),
            'status_progress' => new external_value(PARAM_INT, 'status progress', VALUE_DEFAULT, -1),
        ], VALUE_DEFAULT, []);
    }

    public static function get_tab_filters_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'tab' => new external_value(PARAM_TEXT, 'Current tab'),
            'filtered' => self::define_filters_structure(),
        ]);
    }

    public static function get_tab_filters_returns(): external_single_structure
    {
        $enabled = new external_value(PARAM_BOOL, 'True if filter is enabled', VALUE_DEFAULT, true);

        $select = new external_single_structure([
            "enabled" => $enabled,
            "options" => new external_multiple_structure(
                new external_single_structure([
                    'name'     => new external_value(PARAM_TEXT, 'Option name'),
                    'selected' => new external_value(PARAM_BOOL, 'True if option is selected', VALUE_DEFAULT, false),
                    'value'    => new external_value(PARAM_RAW, 'Option value', VALUE_OPTIONAL),
                ]),
                "Filters options",
                VALUE_DEFAULT,
                []
            ),
        ]);

        $toggle = new external_single_structure([
            "enabled" => $enabled,
            "value" => new external_value(PARAM_RAW, 'Option value', VALUE_OPTIONAL),
            "checked" => new external_value(PARAM_BOOL, 'True if option is selected', VALUE_DEFAULT, false),
        ]);

        return new external_single_structure([
            "status"          => $select,
            "status_progress" => $select,
            "solution_format" => $select,
            "level"           => $select,
            "uf"              => $select,
            "categoryid"      => $select,
            "favourite"       => $toggle,
            "recommended"     => $toggle,
            "most_accessed"   => $toggle,
            'search'          => new external_value(PARAM_RAW, '', VALUE_OPTIONAL),
        ]);
    }

    public static function get_tab_filters(string $tab, array $filtered = [])
    {
        global $CFG;

        require_once($CFG->dirroot . "/local/courseblockapi/lib.php");

        $params = compact('tab', 'filtered');
        $params = self::validate_parameters(self::get_tab_filters_parameters(), $params);
        $context = \context_system::instance();

        require_capability('moodle/category:viewcourselist', $context);

        $tab_filters = tab_filters_factory::factory($params['tab'], $params['filtered']);
        return $tab_filters->export();
    }
}
