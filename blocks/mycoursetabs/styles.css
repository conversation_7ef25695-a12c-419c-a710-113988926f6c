[data-theme="dark"] body .mycoursetabs .nav-link.tablink {
  color: #fff !important;
  border-radius: 0.2rem !important;
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
  font-size: 1rem;
  font-weight: bold;
}

[data-theme="dark"] body .mycoursetabs .nav-link.tablink.active {
  color: var(--primary) !important;
  background-color: var(--light) !important;
}

[data-theme="dark"] body .mycoursetabs .tab-content {
  background-color: transparent !important;
  border: 0 !important;
  box-shadow: none !important;
}

.mycoursetabs:has(.smart-loading.show) .tab-content {
  opacity: 0.4;
}

.mycoursetabs .tab-content .cards:not(:has(.card)) {
  height: 260px;
}

.mycoursetabs .smart-loading {
  position: absolute;
  display: none;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.mycoursetabs .smart-loading.show {
  display: flex;
}

.mycoursetabs .filters .btn-group-toggle label {
  font-size: 1rem;
}

[data-theme="dark"] body .mycoursetabs .btn-info,
body .mycoursetabs .btn-info {
  color: #fff;
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.mycoursetabs .filters label:has(input[name="favourite"]:checked) i:before,
.mycoursetabs .filters label:has(input[name="recommended"]:checked) i:before,
.mycoursetabs .filters label:has(input[name="most_accessed"]:checked) i:before {
  content: "\f205";
}

.mycoursetabs .card:has(.card-course-favourite) .card-link:before {
  content: "";
  background: radial-gradient(
    circle at top left,
    rgba(0, 0, 0, 0.35),
    transparent 20%
  );
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 0.5rem;
}

.mycoursetabs .filters select,
.mycoursetabs .filters .search {
  min-width: 150px;
  margin-bottom: 0.5rem;
}

.mycoursetabs .filters .search-button {
  border-color: var(--bs-gray-700);
}
