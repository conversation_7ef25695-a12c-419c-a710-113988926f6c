<?php
require_once($CFG->dirroot . "/local/courseblockapi/lib.php");

use local_courseblockapi\abstracts\block_courses_base;

class block_mycoursetabs extends block_courses_base
{
    public string $name = '';
    public string $component = '';

    function init()
    {
        $this->name = $this->name();
        $this->component = "block_{$this->name()}";
        $this->title = get_string('pluginname', 'block_mycoursetabs');
    }

    function specialization()
    {
        global $PAGE;

        $PAGE->requires->js_call_amd('block_mycoursetabs/block', 'init');
        //$PAGE->requires->js_call_amd('local_courseblockapi/favourite', 'init', ["mycoursetabs"]);
    }

    public function has_config()
    {
        return true;
    }

    function hide_header()
    {
        return true;
    }

    function applicable_formats()
    {
        return [
            "my" => true,
            "site-index" => true
        ];
    }

    function instance_allow_multiple()
    {
        return false;
    }

    public function get_tabs()
    {
        return [
            (object)[
                "name" => "allcourses",
                "label" => "Todas as Soluções",
                "active" => 0,
            ],
            (object)[
                "name" => "mycourses",
                "label" => "Minhas Soluções",
                "active" => 1,
                "selected" => 1,
            ],
        ];
    }

    function get_content($mobile = false)
    {
        global $PAGE;

        if ($this->content !== NULL) {
            return $this->content;
        }

        $data = new stdClass;
        $data->tabs = $this->get_tabs();
        $data->index_url = (new moodle_url("/blocks/{$this->name}/index.php"))->__toString();

        $renderer = $PAGE->get_renderer("block_mycoursetabs");
        $renderer_data = new \block_mycoursetabs\output\tabs($data);

        $this->content = new stdClass();
        $this->content->text = $renderer->render($renderer_data);
        $this->content->footer = '';
        return $this->content;
    }

    public function can_block_be_added(moodle_page $page): bool
    {
        global $CFG;

        return true;
    }
}
