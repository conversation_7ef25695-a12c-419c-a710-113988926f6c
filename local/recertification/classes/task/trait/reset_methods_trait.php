<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_recertification\task\trait;

use grade_item;
use Exception;
use context_module;
use local_offermanager\persistent\offer_course_model;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/enrollib.php');
require_once($CFG->libdir . '/enrollib.php');
require_once($CFG->dirroot . '/course/lib.php');
require_once($CFG->dirroot . '/local/recertification/lib.php');
require_once($CFG->libdir . '/completionlib.php');
require_once($CFG->libdir . '/gradelib.php');
require_once($CFG->dirroot . '/mod/assign/locallib.php');
require_once($CFG->dirroot . '/mod/quiz/lib.php');
require_once($CFG->dirroot . '/mod/simplecertificate/locallib.php');
require_once($CFG->dirroot . '/mod/scorm/lib.php');


/**
 * Trait reset_methods_trait
 *
 * @package    local_recertification
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait reset_methods_trait
{
    /**
     * Reset and archive completion records
     * @param \int $userid - user id
     * @param \stdClass $course - course record.
     * @param \stdClass $config - recertification config.
     */
    protected function reset_completions($userid, $courseid)
    {
        global $DB;

        $archivecompletiondata = get_config('local_recertification', 'archivecompletiondata');
        $params = array('userid' => $userid, 'course' => $courseid);
        if ($archivecompletiondata) {
            $coursecompletions = $DB->get_records('course_completions', $params);
            $DB->insert_records('local_recertification_cc', $coursecompletions);
            $criteriacompletions = $DB->get_records('course_completion_crit_compl', $params);
            $DB->insert_records('local_recertification_cc_cc', $criteriacompletions);
        }
        $DB->delete_records('course_completions', $params);
        $DB->delete_records('course_completion_crit_compl', $params);

        // Archive and delete all activity completions.
        $selectsql = 'userid = :userid AND coursemoduleid IN (SELECT id FROM {course_modules} WHERE course = :course)';
        if ($archivecompletiondata) {
            $cmc = $DB->get_records_select('course_modules_completion', $selectsql, $params);
            foreach ($cmc as $cid => $unused) {
                // Add courseid to records to help with restore process.
                $cmc[$cid]->course = $courseid;
            }
            $DB->insert_records('local_recertification_cmc', $cmc);
        }
        $DB->delete_records_select('course_modules_viewed', $selectsql, $params);
        $DB->delete_records_select('course_modules_completion', $selectsql, $params);
    }

    /**
     * Reset and archive scorm records.
     * @param \stdclass $userid - user id
     * @param \stdClass $course - course record.
     * @param \stdClass $config - recertification config.
     */
    protected function reset_scorm($userid, $courseid)
    {
        global $DB;

        $scormdata = get_config('local_recertification', 'scormattempts');
        $archivescormdata = get_config('local_recertification', 'archivescormdata');
        if (empty($scormdata)) {
            return;
        } else if ($scormdata == LOCAL_RECERTIFICATION_DELETE) {

            $selectsql = 'userid = :userid AND scormid IN (SELECT id FROM {scorm} WHERE course = :courseid)';
            $params = array('userid' => $userid, 'courseid' => $courseid);

            $scormscoestrack = $DB->get_records_select('scorm_scoes_track', $selectsql, $params);

            if ($archivescormdata) {
                foreach ($scormscoestrack as $sid => $scoes) {
                    // Add courseid to records to help with restore process.
                    $scormscoestrack[$sid]->course = $courseid;
                }
                $DB->insert_records('local_recertification_sst', $scormscoestrack);
            }

            $DB->delete_records_select('scorm_scoes_track', $selectsql, $params);

            foreach ($scormscoestrack as $sid => $scoes) {
                $scorm = $DB->get_record("scorm", array("id" => $scoes->scormid));
                scorm_update_grades($scorm, $userid, true);
            }
        }
    }

    /**
     * Reset attendance records.
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     * @return bool
     */
    protected function reset_attendance($userid, $courseid)
    {
        global $DB;

        $attendancedata = LOCAL_RECERTIFICATION_DELETE;
        $archiveattendancedata = true;

        if (empty($attendancedata) || $attendancedata != LOCAL_RECERTIFICATION_DELETE) {
            return false;
        }

        $course_session_ids = $DB->get_fieldset_sql(
            "SELECT id FROM {attendance_sessions} WHERE attendanceid IN (SELECT id FROM {attendance} WHERE course = ?)",
            array($courseid)
        );

        if (!$course_session_ids) {
            return false;
        }

        list($sessionidsql, $sessionidparams) = $DB->get_in_or_equal($course_session_ids);
        array_unshift($sessionidparams, $userid);

        $attendance_logs = $DB->get_records_select(
            'attendance_log',
            "studentid = ? AND sessionid {$sessionidsql}",
            $sessionidparams
        );

        $tempuser_records = $DB->get_records_select(
            'attendance_tempusers',
            "studentid = :userid AND courseid = :courseid",
            [
                'userid' => $userid,
                'courseid' => $courseid
            ]
        );

        $transaction = $DB->start_delegated_transaction();

        try {
            if ($archiveattendancedata) {
                if ($attendance_logs) {
                    foreach ($attendance_logs as $log) {
                        $histlog = clone ($log);
                        unset($histlog->id);
                        $DB->insert_record('local_recertification_al', $histlog);
                    }
                }
                if ($tempuser_records) {
                    foreach ($tempuser_records as $record) {
                        $hist_record = clone $record;
                        unset($hist_record->id);
                        $DB->insert_record('local_recertification_atl', $hist_record);
                    }
                }
            }

            if ($attendance_logs) {
                $DB->delete_records_select(
                    'attendance_log',
                    "studentid = ? AND sessionid {$sessionidsql}",
                    $sessionidparams
                );
            }

            if ($tempuser_records) {
                $DB->delete_records_select(
                    'attendance_tempusers',
                    "studentid = :userid AND courseid = :courseid",
                    [
                        'userid' => $userid,
                        'courseid' => $courseid
                    ]
                );
            }

            $transaction->allow_commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    /**
     * Reset and archive quiz records.
     * @param \int $userid - userid
     * @param \stdclass $course - course record.
     * @param \stdClass $config - recertification config.
     */
    protected function reset_quiz($userid, $courseid)
    {
        global $DB;

        $quizdata = get_config('local_recertification', 'quizattempts');
        $archivequizdata = get_config('local_recertification', 'archivequizdata');

        if (empty($quizdata)) {
            return;
        } else if ($quizdata == LOCAL_RECERTIFICATION_DELETE) {
            $params = array('userid' => $userid, 'courseid' => $courseid);
            $selectsql = 'userid = :userid AND quiz IN (SELECT id FROM {quiz} WHERE course = :courseid)';
            if ($archivequizdata) {
                $quizattempts = $DB->get_records_select('quiz_attempts', $selectsql, $params);
                foreach ($quizattempts as $qid => $unused) {
                    // Add courseid to records to help with restore process.
                    $quizattempts[$qid]->course = $courseid;
                }
                $DB->insert_records('local_recertification_qa', $quizattempts);

                $quizgrades = $DB->get_records_select('quiz_grades', $selectsql, $params);
                foreach ($quizgrades as $qid => $unused) {
                    // Add courseid to records to help with restore process.
                    $quizgrades[$qid]->course = $courseid;
                }
                $DB->insert_records('local_recertification_qg', $quizgrades);
            }
            $DB->delete_records_select('quiz_attempts', $selectsql, $params);
            $DB->delete_records_select('quiz_grades', $selectsql, $params);
        } else if ($quizdata == LOCAL_RECERTIFICATION_EXTRAATTEMPT) {
            // Get all quizzes that do not have unlimited attempts and have existing data for this user.
            $sql = "SELECT DISTINCT q.*
                      FROM {quiz} q
                      JOIN {quiz_attempts} qa ON q.id = qa.quiz
                     WHERE q.attempts > 0 AND q.course = ? AND qa.userid = ?";
            $quizzes = $DB->get_recordset_sql($sql, array($courseid, $userid));
            foreach ($quizzes as $quiz) {
                // Get number of this users attempts.
                $attempts = \quiz_get_user_attempts($quiz->id, $userid);
                $countattempts = count($attempts);

                // Allow the user to have the same number of attempts at this quiz as they initially did.
                // EG if they can have 2 attempts, and they have 1 attempt already, allow them to have 2 more attempts.
                $nowallowed = $countattempts + $quiz->attempts;

                // Get stuff needed for the events.
                $cm = get_coursemodule_from_instance('quiz', $quiz->id);
                $context = context_module::instance($cm->id);

                $eventparams = array(
                    'context' => $context,
                    'other' => array(
                        'quizid' => $quiz->id
                    ),
                    'relateduserid' => $userid
                );

                $conditions = array(
                    'quiz' => $quiz->id,
                    'userid' => $userid
                );
                if ($oldoverride = $DB->get_record('quiz_overrides', $conditions)) {
                    if ($oldoverride->attempts < $nowallowed) {
                        $oldoverride->attempts = $nowallowed;
                        $DB->update_record('quiz_overrides', $oldoverride);
                        $eventparams['objectid'] = $oldoverride->id;
                        $event = \mod_quiz\event\user_override_updated::create($eventparams);
                        $event->trigger();
                    }
                } else {
                    $data = new \stdClass();
                    $data->attempts = $nowallowed;
                    $data->quiz = $quiz->id;
                    $data->userid = $userid;
                    // Merge quiz defaults with data.
                    $keys = array('timeopen', 'timeclose', 'timelimit', 'password');
                    foreach ($keys as $key) {
                        if (!isset($data->{$key})) {
                            $data->{$key} = $quiz->{$key};
                        }
                    }
                    $newid = $DB->insert_record('quiz_overrides', $data);
                    $eventparams['objectid'] = $newid;
                    $event = \mod_quiz\event\user_override_created::create($eventparams);
                    $event->trigger();
                }
            }
        }
    }

    /**
     * Reset survey records.
     * @param \int $userid - record with user information for recertification
     * @param \stdClass $courseid - course id.
     */
    protected function reset_survey($userid, $courseid)
    {
        global $DB, $USER;

        $USER->id = 2;
        $surveydata = get_config('local_recertification', 'surveyattempts');
        if (empty($surveydata)) {
            return '';
        } else if ($surveydata == LOCAL_RECERTIFICATION_DELETE) {
            $params = array('userid' => $userid, 'courseid' => $courseid);
            $selectsql = 'userid = :userid AND survey IN (SELECT id FROM {survey} WHERE course = :courseid)';
            if (get_config('local_recertification', 'archivesurveydata')) {
                $answers = $DB->get_records_select('survey_answers', $selectsql, $params);

                foreach ($answers as $answer) {

                    $obj = new \stdClass;
                    $obj->survey = $answer->survey;
                    $obj->userid = $answer->userid;
                    $obj->question = $answer->question;
                    $obj->time = $answer->time;
                    $obj->answer1 = $answer->answer1;
                    $obj->answer2 = $answer->answer2;

                    $DB->insert_record('local_recertification_su', $obj);
                }
            }

            $DB->delete_records_select('survey_answers', $selectsql, $params);
            $DB->delete_records_select('survey_analysis', $selectsql, $params);
        }
    }
    /**
     * Reset assign records.
     * @param \int $userid - record with user information for recertification
     * @param \stdClass $course - course record.
     * @param \stdClass $config - recertification config.
     */
    protected function reset_assign($userid, $courseid)
    {
        global $DB, $USER;

        $USER->id = 2;
        $assigndata = get_config('local_recertification', 'assignattempts');
        if (empty($assigndata)) {
            return '';
        } else if ($assigndata == LOCAL_RECERTIFICATION_EXTRAATTEMPT) {
            $sql = "SELECT DISTINCT a.*
                      FROM {assign} a
                      JOIN {assign_submission} s ON a.id = s.assignment
                     WHERE a.course = ? AND s.userid = ?";
            $assigns = $DB->get_recordset_sql($sql, array($courseid, $userid));
            $nopermissions = false;
            foreach ($assigns as $assign) {
                $cm = get_coursemodule_from_instance('assign', $assign->id);
                $context = context_module::instance($cm->id);
                if (has_capability('mod/assign:grade', $context)) {
                    // Assign add_attempt() is protected - use reflection so we don't have to write our own.
                    $r = new \ReflectionMethod('assign', 'add_attempt');
                    $r->setAccessible(true);
                    $r->invoke(new \assign($context, $cm, ['id' => $courseid]), $userid);
                } else {
                    $nopermissions = true;
                }
            }
            if ($nopermissions) {
                return get_string('noassigngradepermission', 'local_recertification');
            }
        } else if ($assigndata == LOCAL_RECERTIFICATION_DELETE) {

            $submissions = $DB->get_records_sql(
                "SELECT DISTINCT s.*
                FROM {assign_submission} s
                JOIN {assign} a ON a.id = s.assignment
                WHERE a.course = ? AND s.userid = ?
                ",
                array($courseid, $userid)
            );

            foreach ($submissions as $submission) {

                $grades = $DB->get_records_sql(
                    "SELECT *
                    FROM {assign_grades}
                    WHERE assignment = :assignment
                    AND userid = :userid
                    ",
                    ['assignment' => $submission->assignment, 'userid' => $userid]
                );

                foreach ($grades as $grade) {

                    if (get_config('local_recertification', 'archiveassigndata')) {

                        $obj = new \stdClass;
                        $obj->assignment = $grade->assignment;
                        $obj->userid = $grade->userid;
                        $obj->timecreated = $grade->timecreated;
                        $obj->timemodified = $grade->timemodified;
                        $obj->grader = $grade->grader;
                        $obj->grade = $grade->grade;
                        $obj->attemptnumber = $grade->attemptnumber;

                        $DB->insert_record('local_recertification_ag', $obj);
                    }

                    $DB->delete_records('assign_grades', ['id' => $grade->id]);
                }

                if (get_config('local_recertification', 'archiveassigndata')) {

                    $obj = new \stdClass;
                    $obj->assignment = $submission->assignment;
                    $obj->userid = $submission->userid;
                    $obj->timecreated = $submission->timecreated;
                    $obj->timemodified = $submission->timemodified;
                    $obj->status = $submission->status;
                    $obj->groupid = $submission->groupid;
                    $obj->attemptnumber = $submission->attemptnumber;
                    $obj->latest = $submission->latest;

                    $DB->insert_record('local_recertification_as', $obj);
                }

                $DB->delete_records('assign_submission', ['id' => $submission->id]);
            }
        }
    }


    /**
     * Reset supervideo data.
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     */
    protected function reset_supervideo($userid, $courseid)
    {
        global $DB;

        $archivecompletiondata = true;
        $params = array('userid' => $userid, 'course' => $courseid);

        $supervideo_views = $DB->get_records_sql(
            "SELECT *
                FROM {supervideo_view}
                WHERE user_id = :userid
                    AND cm_id IN (SELECT id FROM {course_modules} WHERE course = :course)
            ",
            $params
        );

        $transaction = $DB->start_delegated_transaction();

        try {
            if ($archivecompletiondata) {
                if ($supervideo_views) {
                    foreach ($supervideo_views as $supervideo_view) {
                        $hist_supervideo_view = clone ($supervideo_view);
                        unset($hist_supervideo_view->id);
                        $DB->insert_record('local_recertification_sw', $hist_supervideo_view);
                    }
                }
            }

            $DB->delete_records_select(
                'supervideo_view',
                "user_id = :userid AND cm_id IN (SELECT id FROM {course_modules} WHERE course = :course)",
                $params
            );

            $transaction->allow_commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    /**
     * Reset feedback records.
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     * @return bool
     */
    protected function reset_feedback($userid, $courseid)
    {
        global $DB;

        $feedbackdata = LOCAL_RECERTIFICATION_DELETE;
        $archivefeedbackdata = true;

        if (empty($feedbackdata) || $feedbackdata != LOCAL_RECERTIFICATION_DELETE) {
            return false;
        }

        $feedbackids = $DB->get_fieldset_select(
            'feedback',
            'id',
            'course = :courseid',
            [
                'courseid' => $courseid
            ]
        );

        if (empty($feedbackids)) {
            return false;
        }

        list($feedbacksql, $feedbackparams) = $DB->get_in_or_equal($feedbackids);
        array_unshift($feedbackparams, $userid);

        $feedbackcompleted = $DB->get_records_select('feedback_completed', "userid = ? AND feedback $feedbacksql", $feedbackparams);
        $feedbackcompletedtmp = $DB->get_records_select('feedback_completedtmp', "userid = ? AND feedback $feedbacksql", $feedbackparams);

        $transaction = $DB->start_delegated_transaction();

        try {
            if ($archivefeedbackdata) {
                if ($feedbackcompleted) {
                    foreach ($feedbackcompleted as $completed) {
                        $histcompleted = clone ($completed);
                        unset($histcompleted->id);
                        $DB->insert_record('local_recertification_fbc', $histcompleted);
                    }
                }
                if ($feedbackcompletedtmp) {
                    foreach ($feedbackcompletedtmp as $completedtmp) {
                        $histcompletedtmp = clone ($completedtmp);
                        unset($histcompletedtmp->id);
                        $DB->insert_record('local_recertification_fbct', $histcompletedtmp);
                    }
                }
            }

            if ($feedbackcompleted) {
                $DB->delete_records_select('feedback_completed', "userid = ? AND feedback $feedbacksql", $feedbackparams);
            }
            if ($feedbackcompletedtmp) {
                $DB->delete_records_select('feedback_completedtmp', "userid = ? AND feedback $feedbacksql", $feedbackparams);
            }

            $transaction->allow_commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    public function test_reset_forum($userid, $courseid)
    {
        return $this->reset_forum($userid, $courseid);
    }

    /**
     * Reset and archive forum data including posts, discussions, subscriptions,
     * read tracking, ratings, grades and queue items.
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     * @return bool
     */
    protected function reset_forum($userid, $courseid)
    {
        global $DB;

        $forumdata = LOCAL_RECERTIFICATION_DELETE;
        $archiveforumdata = true;

        if (empty($forumdata) || $forumdata != LOCAL_RECERTIFICATION_DELETE) {
            return false;
        }

        $forumids = $DB->get_fieldset_select(
            'forum',
            'id',
            'course = :courseid',
            [
                'courseid' => $courseid
            ]
        );

        if (empty($forumids)) {
            return false;
        }

        $discussion_submissions = $readposts = $posts = $queueitems = null;

        list($forumsql, $forumparams) = $DB->get_in_or_equal($forumids);

        array_unshift($forumparams, $userid);

        $grades = $DB->get_records_select('forum_grades', "userid = ? AND forum $forumsql", $forumparams);

        $subscriptions = $DB->get_records_select('forum_subscriptions', "userid = ? AND forum $forumsql", $forumparams);

        $digests = $DB->get_records_select('forum_digests', "userid = ? AND forum $forumsql", $forumparams);

        $track_prefs = $DB->get_records_select('forum_track_prefs', "userid = ? AND forumid $forumsql", $forumparams);

        $readposts = $DB->get_records_select('forum_read', "userid = ? AND forumid $forumsql", $forumparams);

        $discussions = $DB->get_records_select('forum_discussions', "userid = ? AND forum $forumsql", $forumparams);

        if ($discussions) {

            $discussionids = array_keys($discussions);

            list($discussionsql, $discussionparams) = $DB->get_in_or_equal($discussionids);

            array_unshift($discussionparams, $userid);

            $discussion_submissions = $DB->get_records_select('forum_discussion_subs', "userid = ? AND discussion {$discussionsql}", $discussionparams);

            $posts = $DB->get_records_select('forum_posts', "userid = ? AND discussion {$discussionsql}", $discussionparams);

            $queueitems = $DB->get_records_select('forum_queue', "userid = ? AND discussionid $discussionsql", $discussionparams);
        }

        $transaction = $DB->start_delegated_transaction();

        try {
            if ($archiveforumdata) {
                if ($grades) {
                    foreach ($grades as $grade) {
                        $histgrade = clone ($grade);
                        unset($histgrade->id);
                        $DB->insert_record('local_recertification_fg', $histgrade);
                    }
                }
                if ($subscriptions) {
                    foreach ($subscriptions as $subscription) {
                        $histsub = clone ($subscription);
                        unset($histsub->id);
                        $DB->insert_record('local_recertification_fs', $histsub);
                    }
                }
                if ($digests) {
                    foreach ($digests as $digest) {
                        $histdigest = clone ($digest);
                        unset($histdigest->id);
                        $DB->insert_record('local_recertification_fdg', $histdigest);
                    }
                }
                if ($track_prefs) {
                    foreach ($track_prefs as $track_pref) {
                        $hist_track_prefs = clone ($track_pref);
                        unset($hist_track_prefs->id);
                        $DB->insert_record('local_recertification_ftp', $hist_track_prefs);
                    }
                }
                if ($readposts) {
                    foreach ($readposts as $readpost) {
                        $histread = clone ($readpost);
                        unset($histread->id);
                        $DB->insert_record('local_recertification_frp', $histread);
                    }
                }
                if ($queueitems) {
                    foreach ($queueitems as $queueitem) {
                        $histqueue = clone ($queueitem);
                        unset($histqueue->id);
                        $DB->insert_record('local_recertification_fqi', $histqueue);
                    }
                }
                if ($posts) {
                    foreach ($posts as $post) {
                        $histpost = clone ($post);
                        unset($post->id);
                        $DB->insert_record('local_recertification_fp', $histpost);
                    }
                }
                if ($discussion_submissions) {
                    foreach ($discussion_submissions as $discussion_submission) {
                        $histdiscussion_submission = clone ($discussion_submission);
                        unset($histdiscussion_submission->id);
                        $DB->insert_record('local_recertification_fds', $histdiscussion_submission);
                    }
                }
                if ($discussions) {
                    foreach ($discussions as $discussion) {
                        $histdiscussion = clone ($discussion);
                        unset($histdiscussion->id);
                        $DB->insert_record('local_recertification_fd', $histdiscussion);
                    }
                }
            }

            if ($grades) {
                $gradeids = array_keys($grades);
                $DB->delete_records_list('forum_grades', 'id', $gradeids);
            }

            if ($subscriptions) {
                $subscriptionids = array_keys($subscriptions);
                $DB->delete_records_list('forum_subscriptions', 'id', $subscriptionids);
            }

            if ($digests) {
                $digestsids = array_keys($digests);
                $DB->delete_records_list('forum_digests', 'id', $digestsids);
            }

            if ($track_prefs) {
                $track_pref_ids = array_keys($track_prefs);
                $DB->delete_records_list('forum_track_prefs', 'id', $track_pref_ids);
            }

            if ($readposts) {
                $readpostids = array_keys($readposts);
                $DB->delete_records_list('forum_read', 'id', $readpostids);
            }

            if ($discussion_submissions) {
                $discussion_submission_ids = array_keys($discussion_submissions);
                $DB->delete_records_list('forum_discussion_subs', 'id', $discussion_submission_ids);
            }

            if ($posts) {
                $postids = array_keys($posts);
                $DB->delete_records_list('forum_posts', 'id', $postids);
            }

            if ($queueitems) {
                $queueids = array_keys($queueitems);
                $DB->delete_records_list('forum_queue', 'id', $queueids);
            }

            if ($discussions) {
                $DB->delete_records_list('forum_discussions', 'id', $discussionids);
            }

            $transaction->allow_commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    /**
     * Reset and archive NPS data from nps_completed and nps_completedtmp.
     *
     * @param int $userid User ID.
     * @param int $courseid Course ID.
     * @return bool True on success, false on failure or if action is not 'delete'.
     * @throws dml_exception If a database error occurs.
     */
    protected function reset_nps($userid, $courseid)
    {
        global $DB;

        $npsdataaction = LOCAL_RECERTIFICATION_DELETE;
        $archivenpsdata = true;

        if (empty($npsdataaction) || $npsdataaction != LOCAL_RECERTIFICATION_DELETE) {
            return false;
        }

        $nps_ids = $DB->get_fieldset_select(
            'nps',
            'id',
            'course = :courseid',
            ['courseid' => $courseid]
        );

        if (!$nps_ids) {
            return false;
        }

        list($npssql, $npsparams) = $DB->get_in_or_equal($nps_ids);

        array_unshift($npsparams, $userid);

        $completed_records = $DB->get_records_select(
            'nps_completed',
            "userid = ? AND nps {$npssql}",
            $npsparams
        );

        $completedtmp_records = $DB->get_records_select(
            'nps_completedtmp',
            "userid = ? AND nps {$npssql}",
            $npsparams
        );

        if (empty($completed_records) && empty($completedtmp_records)) {
            return true;
        }

        $transaction = $DB->start_delegated_transaction();

        try {
            if ($archivenpsdata) {
                if (!empty($completed_records)) {
                    foreach ($completed_records as $record) {
                        $hist_record = clone ($record);
                        unset($hist_record->id);
                        $DB->insert_record('local_recertification_nc', $hist_record);
                    }
                }

                if (!empty($completedtmp_records)) {
                    foreach ($completedtmp_records as $record_tmp) {
                        $hist_record_tmp = clone ($record_tmp);
                        unset($hist_record_tmp->id);
                        $DB->insert_record('local_recertification_nct', $hist_record_tmp);
                    }
                }
            }

            if (!empty($completed_records)) {
                $completed_ids = array_keys($completed_records);
                $DB->delete_records_list('nps_completed', 'id', $completed_ids);
            }

            if (!empty($completedtmp_records)) {
                $completedtmp_ids = array_keys($completedtmp_records);
                $DB->delete_records_list('nps_completedtmp', 'id', $completedtmp_ids);
            }

            $transaction->allow_commit();
            return true;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }

        return false;
    }

    /** Reset Simplecertificate
     * @param int $userid
     * @param int $courseid
     * @param int $ueid
     * @return bool if activity was reseted
     */
    protected function reset_simplecertificate($userid, $courseid, $ueid = null)
    {
        global $DB;

        
        if ($ueid) {
            $ue = $DB->get_record('user_enrolments', ['id' => $ueid]);
            $item = new \stdClass;
            $item->ueid = $ue->id ?? false;
        } else {
            $item = $DB->get_record_sql(
                "SELECT *
                FROM {local_recertification_queue}
                WHERE  userid = :userid
                AND courseid = :courseid
                LIMIT 0,1
                ",
                ['userid' => $userid, 'courseid' => $courseid]
            );
        }

        if (!$item) {
            return false;
        }

        $cm = $DB->get_record_sql(
            "SELECT cm.id , cm.instance
            FROM {course_modules} cm
            LEFT JOIN {modules} m ON (cm.module = m.id)
            WHERE cm.course = :courseid
            AND m.name = 'simplecertificate'
            LIMIT 0,1
            ",
            ['courseid' => $courseid]
        );

        if (!$cm) {
            return false;
        }

        $cm_id = $cm->id;
        $cm_instance = $cm->instance;
        $cm_context = context_module::instance($cm_id);

        $user = (object)[
            'id' => $userid,
        ];

        try {
            $simplecertificate = new \simplecertificate($cm_context);
            $issuedcert = $simplecertificate->get_issue($user, true); // Forcing the certificate to exist;

            if (!$issuedcert) {
                return false;
            }

            $storage_path = $this->create_directory($userid, $courseid, $item->ueid, $cm_id);

            if ($relative_file_path = $this->save_simplecertificate_file($issuedcert, $storage_path)) {
                $obj = new \stdClass;
                // Storing the backup file path on the database:
                $obj->ueid = $item->ueid;
                $obj->courseid = $courseid;
                $obj->userid = $userid;
                $obj->code = $issuedcert->code;
                $obj->fileurl = $relative_file_path;
                $obj->coursename = $DB->get_field('course', 'fullname', ['id' => $courseid]);
                $obj->emissiontime = $issuedcert->timecreated;
                $obj->timecreated = time();
                $obj->certtype = 'simplecert'; // Identificar como SimpleCertificate

                if ($finalgrade = $this->get_finalgrade($userid, $courseid)) {
                    $obj->finalgrade = $finalgrade;
                }

                if ($this->save_simplecertificate_state_in_histoty($obj)) {
                    $this->delete_simplecertificate_file($issuedcert);
                    $this->delete_simplecertificate_state($userid, $cm_instance);

                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            mtrace($e->getMessage());
            return null;
        }
    }

    protected function save_simplecertificate_file(&$issuedcert, $storage_path)
    {
        global $CFG;
        // Getting certificate file and copying it to the backup folder
        $fs = get_file_storage();

        if (!$fs->file_exists_by_hash($issuedcert->pathnamehash)) {
            return false;
        }

        $file = $fs->get_file_by_hash($issuedcert->pathnamehash);

        $file_path = null;

        if (!empty($file)) {

            // Generating the storage path for this backup:
            $filename = $file->get_filename();
            $file_path = $storage_path . $filename;

            if ($file->copy_content_to($file_path)) {

                $relative_file_path = str_replace("{$CFG->dataroot}/", '', $file_path);
                return $relative_file_path;
            }

            return false;
        }

        return true;
    }

    private function save_simplecertificate_state_in_histoty($record)
    {
        global $DB;

        $id = $DB->insert_record('local_recertification_sc', $record);

        return $id ?: false;
    }

    private function delete_simplecertificate_file(&$issuedcert)
    {

        $fs = get_file_storage();

        if (!$fs->file_exists_by_hash($issuedcert->pathnamehash)) {
            return false;
        }

        $file = $fs->get_file_by_hash($issuedcert->pathnamehash);

        if (!empty($file)) {
            $file->delete();
            return true;
        }

        return false;
    }

    private function create_directory($userid, $courseid, $ueid, $cmid)
    {
        global $CFG;

        $storage_path = $CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/{$courseid}/{$ueid}/{$cmid}/"; // Like {$userid}/{$courseid}/{$ueid}/{$cmid}/

        if (!is_dir($CFG->dataroot . "/filedir/simplecertificate_hist/")) {
            mkdir($CFG->dataroot . "/filedir/simplecertificate_hist/");
        }
        if (!is_dir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}")) {
            mkdir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/");
        }
        if (!is_dir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/{$courseid}")) {
            mkdir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/{$courseid}");
        }
        if (!is_dir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/{$courseid}/{$ueid}")) {
            mkdir($CFG->dataroot . "/filedir/simplecertificate_hist/{$userid}/{$courseid}/{$ueid}");
        }
        if (!is_dir($storage_path)) {
            mkdir($storage_path);
        }

        return $storage_path;
    }

    private function create_customcert_directory($userid, $courseid, $ueid, $cmid)
    {
        global $CFG;

        $storage_path = $CFG->dataroot . "/filedir/customcert_hist/{$userid}/{$courseid}/{$ueid}/{$cmid}/";

        if (!is_dir($CFG->dataroot . "/filedir/customcert_hist/")) {
            mkdir($CFG->dataroot . "/filedir/customcert_hist/");
        }
        if (!is_dir($CFG->dataroot . "/filedir/customcert_hist/{$userid}")) {
            mkdir($CFG->dataroot . "/filedir/customcert_hist/{$userid}/");
        }
        if (!is_dir($CFG->dataroot . "/filedir/customcert_hist/{$userid}/{$courseid}")) {
            mkdir($CFG->dataroot . "/filedir/customcert_hist/{$userid}/{$courseid}");
        }
        if (!is_dir($CFG->dataroot . "/filedir/customcert_hist/{$userid}/{$courseid}/{$ueid}")) {
            mkdir($CFG->dataroot . "/filedir/customcert_hist/{$userid}/{$courseid}/{$ueid}");
        }
        if (!is_dir($storage_path)) {
            mkdir($storage_path);
        }

        return $storage_path;
    }

    private function delete_simplecertificate_state($userid, $cm_instance)
    {
        global $DB;

        return $DB->delete_records('simplecertificate_issues', array(
            'userid' => $userid,
            'certificateid' => $cm_instance,
        ));
    }

    private function get_finalgrade($userid, $courseid)
    {
        global $DB;

        return $DB->get_field_sql(
            "SELECT gg.finalgrade
            FROM {grade_items} gi
            JOIN {grade_grades} gg ON (gg.itemid = gi.id)
            WHERE gi.courseid = :courseid
            AND gg.userid = :userid
            AND gi.itemtype = 'course'
            LIMIT 0,1
            ",
            ['courseid' => $courseid, 'userid' => $userid]
        );
    }

    private function delete_grades($userid, $courseid)
    {
        global $DB;
        $grade_grade_ids = $DB->get_fieldset_sql(
            "SELECT gg.id
            FROM {grade_grades} gg
                JOIN {grade_items} gi ON (gi.id = gg.itemid)
            WHERE gg.userid = :userid
                AND gi.courseid = :courseid
            ",
            [
                'userid' => $userid,
                'courseid' => $courseid
            ]
        );

        if (!$grade_grade_ids) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($grade_grade_ids);

        $DB->delete_records_select('grade_grades', "id {$insql}", $inparams);
    }

    /** Reset CustomCert
     * @param int $userid
     * @param int $courseid
     * @param int $ueid
     * @return bool
     */
    protected function reset_customcert($userid, $courseid, $ueid = null)
    {
        global $CFG, $DB;

        // Verificar se o módulo customcert está instalado
        if (!$DB->record_exists('modules', ['name' => 'customcert', 'visible' => 1])) {
            return false;
        }

        if ($ueid) {
            $ue = $DB->get_record('user_enrolments', ['id' => $ueid]);
            $item = new \stdClass;
            $item->ueid = $ue->id ?? false;
        } else {
            $item = $DB->get_record_sql(
                "SELECT *
                FROM {local_recertification_queue}
                WHERE  userid = :userid
                AND courseid = :courseid
                LIMIT 0,1
                ",
                ['userid' => $userid, 'courseid' => $courseid]
            );
        }

        if (!$item) {
            return false;
        }

        $cm = $DB->get_record_sql(
            "SELECT cm.id , cm.instance
            FROM {course_modules} cm
            LEFT JOIN {modules} m ON (cm.module = m.id)
            WHERE cm.course = :courseid
            AND m.name = 'customcert'
            LIMIT 0,1
            ",
            ['courseid' => $courseid]
        );

        if (!$cm) {
            return false;
        }

        $cm_id = $cm->id;
        $cm_instance = $cm->instance;

        try {
            // Buscar o certificado emitido para o usuário
            $issue = $DB->get_record('customcert_issues', [
                'customcertid' => $cm_instance,
                'userid' => $userid
            ]);

            if (!$issue) {
                return false; // Usuário não tem certificado emitido
            }

            // Buscar informações do certificado
            $customcert = $DB->get_record('customcert', ['id' => $cm_instance]);
            if (!$customcert) {
                return false;
            }

            // Gerar o PDF do certificado para backup
            $template = $DB->get_record('customcert_templates', ['id' => $customcert->templateid]);
            if (!$template) {
                return false;
            }

            $template_obj = new \mod_customcert\template($template);
            $pdf_content = $template_obj->generate_pdf(false, $userid, true, $customcert->id);

            if ($pdf_content) {
                // Criar diretório específico para CustomCert
                $storage_path = $this->create_customcert_directory($userid, $courseid, $item->ueid, $cm_id);

                // Salvar o PDF no diretório de backup
                $filename = 'certificate_' . $issue->code . '.pdf';
                $file_path = $storage_path . $filename;

                if (file_put_contents($file_path, $pdf_content)) {
                    // Salvar informações do certificado no histórico
                    $obj = new \stdClass;
                    $obj->ueid = $item->ueid;
                    $obj->userid = $userid;
                    $obj->courseid = $courseid;
                    $obj->customcertid = $cm_instance;
                    $obj->code = $issue->code;
                    $obj->fileurl = str_replace("{$CFG->dataroot}/", '', $file_path);
                    $obj->coursename = $DB->get_field('course', 'fullname', ['id' => $courseid]);
                    $obj->emissiontime = $issue->timecreated;
                    $obj->timecreated = time();

                    if ($finalgrade = $this->get_finalgrade($userid, $courseid)) {
                        $obj->finalgrade = $finalgrade;
                    }

                    // Salvar no histórico usando a mesma tabela do SimpleCertificate
                    if ($this->save_customcert_state_in_history($obj)) {
                        // Deletar o registro do certificado emitido
                        $this->delete_customcert_state($userid, $cm_instance);
                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            mtrace('Erro ao resetar CustomCert: ' . $e->getMessage());
            return false;
        }
    }

    private function save_customcert_state_in_history($record)
    {
        global $DB;

        // Determinar qual tabela usar para CustomCert
        $table_name = $this->get_customcert_table_name();

        $id = $DB->insert_record($table_name, $record);
        return $id ?: false;
    }

    private function get_customcert_table_name()
    {
        global $DB;

        // Verificar se existe a tabela local_recertification_cct (nome alternativo)
        if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cct'))) {
            return 'local_recertification_cct';
        }

        // Verificar se local_recertification_cc existe e tem a estrutura correta para CustomCert
        if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cc'))) {
            $columns = $DB->get_columns('local_recertification_cc');
            if (isset($columns['customcertid'])) {
                return 'local_recertification_cc';
            }
        }

        // Fallback para local_recertification_cc
        return 'local_recertification_cc';
    }

    private function delete_customcert_state($userid, $cm_instance)
    {
        global $DB;

        return $DB->delete_records('customcert_issues', [
            'userid' => $userid,
            'customcertid' => $cm_instance,
        ]);
    }
}
