<?php

require('../../config.php');
require_login();

if (!get_config('local_recertification', 'enablerecertification')) {
    redirect($CFG->wwwroot . '/', get_string('notenabled', 'local_recertification'), null, \core\output\notification::NOTIFY_ERROR);
}

require_capability('local/recertification:manage', context_system::instance());

$certificates = required_param('certificates', PARAM_TEXT);
$type = optional_param('type', 'simple', PARAM_ALPHA); // 'simple' ou 'custom'
$certificates = explode(',', $certificates);

list($insql, $inparams) = $DB->get_in_or_equal($certificates);

// Determinar qual tabela usar baseado no tipo
if ($type === 'custom') {
    $table = local_recertification_get_customcert_table_name();
} else {
    $table = 'local_recertification_sc';
}

$files = $DB->get_records_sql_menu(
    "SELECT id,fileurl
    FROM {{$table}}
    WHERE id {$insql}
    ",
    $inparams
);

if (count($files) > 1) {

    $zip = new ZipArchive;
    $zipname = 'certificados.zip';

    if (file_exists($zipname)) {
        unlink($zipname);
    }

    if ($zip->open($zipname, ZipArchive::CREATE) === TRUE) {

        foreach ($files as $file) {

            $filepath = "{$CFG->dataroot}/{$file}";
            $filename = explode('/', $file);
            $filename = array_pop($filename);

            if (file_exists($filepath)) {
                $zip->addFile($filepath, $filename);
            }
        }

        $zip->close();

        header('Content-Type: application/zip');
        header('Content-disposition: attachment; filename=' . $zipname);
        header('Content-Length: ' . filesize($zipname));

        ob_clean();
        flush();
        readfile($zipname);

        unlink($zipname);

        exit;
    }
} else {

    $files = reset($files);
    $filename = explode('/', $files);
    $filename = array_pop($filename);
    $filepath = "{$CFG->dataroot}/{$files}";

    if (file_exists($filepath)) {

        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header("Content-Type: application/force-download");
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . urlencode($filename) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filepath));

        ob_clean();
        flush();
        readfile($filepath);

        exit;
    } else {
        print_error('Arquivo de certificado não encontrado');
    }
}
