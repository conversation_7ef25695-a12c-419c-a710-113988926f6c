<?php

defined('MOODLE_INTERNAL') || die();

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_certificates($userid)
{
    $simplecertificates = local_certificatepage_get_user_simple_certificates($userid);
    $customcertificates = local_certificatepage_get_user_custom_certificates($userid);

    $withlinkedinmodule = get_config('local_certificatepage', 'viewlinkedincertificates');

    if($withlinkedinmodule && has_mod_linkedin()) {
        $linkedincertificates = local_certificatepage_get_user_linkedin_certificates($userid);

        $allusercertificates = array_merge($simplecertificates, $customcertificates, $linkedincertificates);

    } else {
        $allusercertificates = array_merge($simplecertificates, $customcertificates);
    }

    uasort($allusercertificates, function($certificate, $nextcertificate) {
        return $nextcertificate->timecreated > $certificate->timecreated;
    });

    return $allusercertificates;
}

/**
 * Check if mod_linkedin is installed.
 * @return bool
 */
function has_mod_linkedin()
{
    global $DB;

    return $haslinkedinmod = $DB->record_exists(
        $table = 'modules',
        $conditions = [
            'name' => 'linkedin',
            'visible' => 1
        ]
    );
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_simple_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql_active = <<<SQL

SELECT  issues.*,
        null cover,
        cm.id module,
        cm.course course,
        'active' as source
FROM {simplecertificate} cert

  JOIN {simplecertificate_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id
  JOIN {course} c
    ON c.id = cert.course

WHERE issues.userid = :userid
AND m.name = :name
AND c.format <> 'trails'
-- AND cm.visible = :visible
SQL;

    $param_active = [
        'userid'  => $userid,
        'name'    => 'simplecertificate',
        'visible' => 1
    ];

    $certificates = [];

    if ($active_certificates = $DB->get_records_sql($sql_active, $param_active)) {
        $certificates = array_merge($certificates, $active_certificates);
    }

    $history_certificates = local_certificatepage_get_user_simple_certificates_from_history($userid);
    if ($history_certificates) {
        $certificates = array_merge($certificates, $history_certificates);
    }

    $placeholder = "pix/cinza-placeholder.jpg";
    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'simplecertificate';
        }
    }

    return $certificates ?: [];
}

/**
 * Buscar certificados SimpleCertificate do histórico de recertificação
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_simple_certificates_from_history($userid)
{
    global $DB;

    if (!$DB->get_manager()->table_exists(new \xmldb_table('local_recertification_sc'))) {
        return [];
    }

    $sql = <<<SQL

SELECT  hist.*,
        hist.coursename,
        null cover,
        hist.courseid as course,
        hist.courseid as module,
        'history' as source
FROM {local_recertification_sc} hist
  JOIN {course} c
    ON c.id = hist.courseid

WHERE hist.userid = :userid
AND c.format <> 'trails'
SQL;

    $param = [
        'userid' => $userid
    ];

    return $DB->get_records_sql($sql, $param) ?: [];
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_custom_certificates($userid)
{
    global $DB, $OUTPUT;

    // Verificar se o plugin customcert está instalado
    if (!$DB->record_exists('modules', ['name' => 'customcert', 'visible' => 1])) {
        return [];
    }

    $sql_active = <<<SQL

SELECT  issues.*,
        issues.customcertid as certificateid,
        cert.name as certificatename,
        c.fullname as coursename,
        null cover,
        cm.id module,
        cm.course course,
        'active' as source
FROM {customcert} cert

  JOIN {customcert_issues} issues
    ON cert.id = issues.customcertid
  JOIN {course} c
    ON c.id = cert.course
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
AND c.format <> 'trails'
-- AND cm.visible = :visible
SQL;

    $param_active = [
        'userid'  => $userid,
        'name'    => 'customcert',
        'visible' => 1
    ];

    $certificates = [];

    if ($active_certificates = $DB->get_records_sql($sql_active, $param_active)) {
        $certificates = array_merge($certificates, $active_certificates);
    }

    $history_certificates = local_certificatepage_get_user_custom_certificates_from_history($userid);
    if ($history_certificates) {
        $certificates = array_merge($certificates, $history_certificates);
    }

    $placeholder = "pix/cinza-placeholder.jpg";
    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'customcert';
        }
    }

    return $certificates ?: [];
}

/**
 * Buscar certificados CustomCert do histórico de recertificação
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_custom_certificates_from_history($userid)
{
    global $DB;

    $table_name = local_certificatepage_get_customcert_table_name();

    if (!$table_name || !$DB->get_manager()->table_exists(new \xmldb_table($table_name))) {
        return [];
    }

    try {
        $sql = <<<SQL

SELECT  hist.*,
        hist.customcertid as certificateid,
        hist.coursename,
        null as cover,
        hist.courseid as course,
        hist.courseid as module,
        'history' as source,
        'customcert' as modname
FROM {{$table_name}} hist
  JOIN {course} c
    ON c.id = hist.courseid

WHERE hist.userid = :userid
AND c.format <> 'trails'
SQL;

        $param = [
            'userid' => $userid
        ];

        $results = $DB->get_records_sql($sql, $param);
        return $results ?: [];

    } catch (Exception $e) {
        error_log('Erro ao buscar certificados CustomCert do histórico: ' . $e->getMessage());
        return [];
    }
}

/**
 * Determinar qual tabela usar para CustomCert no histórico
 * @return string|null
 */
function local_certificatepage_get_customcert_table_name()
{
    global $DB;

    if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cct'))) {
        return 'local_recertification_cct';
    }

    if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cc'))) {
        try {
            $columns = $DB->get_columns('local_recertification_cc');
            if (isset($columns['customcertid'])) {
                return 'local_recertification_cc';
            }
        } catch (Exception $e) {
            // Se houver erro ao verificar as colunas, continuar
        }
    }

    return null;
}

/**
 * Função de debug para verificar certificados CustomCert no histórico
 * @param $userid
 * @return array
 */
function local_certificatepage_debug_customcert_history($userid)
{
    global $DB;

    $debug_info = [];
    $debug_info['userid'] = $userid;

    // Verificar tabelas existentes
    $debug_info['tables'] = [];
    $debug_info['tables']['simplecertificate_issues'] = $DB->get_manager()->table_exists(new \xmldb_table('simplecertificate_issues'));
    $debug_info['tables']['customcert_issues'] = $DB->get_manager()->table_exists(new \xmldb_table('customcert_issues'));
    $debug_info['tables']['local_recertification_sc'] = $DB->get_manager()->table_exists(new \xmldb_table('local_recertification_sc'));
    $debug_info['tables']['local_recertification_cct'] = $DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cct'));
    $debug_info['tables']['local_recertification_cc'] = $DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cc'));

    // Verificar qual tabela está sendo usada para CustomCert
    $debug_info['custom_table_selected'] = local_certificatepage_get_customcert_table_name();

    // === CERTIFICADOS SIMPLES HISTÓRICO ===
    if ($debug_info['tables']['local_recertification_sc']) {
        try {
            $sql = "SELECT COUNT(*) FROM {local_recertification_sc} WHERE userid = :userid";
            $debug_info['simple_history_count'] = $DB->get_field_sql($sql, ['userid' => $userid]);

            $sql = "SELECT * FROM {local_recertification_sc} WHERE userid = :userid LIMIT 3";
            $debug_info['simple_history_sample'] = $DB->get_records_sql($sql, ['userid' => $userid]);
        } catch (Exception $e) {
            $debug_info['simple_history_error'] = $e->getMessage();
        }
    }

    // === CERTIFICADOS CUSTOMCERT HISTÓRICO ===
    if ($debug_info['custom_table_selected']) {
        try {
            $sql = "SELECT COUNT(*) FROM {{$debug_info['custom_table_selected']}} WHERE userid = :userid";
            $debug_info['custom_history_count'] = $DB->get_field_sql($sql, ['userid' => $userid]);

            $sql = "SELECT * FROM {{$debug_info['custom_table_selected']}} WHERE userid = :userid LIMIT 3";
            $debug_info['custom_history_sample'] = $DB->get_records_sql($sql, ['userid' => $userid]);
        } catch (Exception $e) {
            $debug_info['custom_history_error'] = $e->getMessage();
        }
    }

    // === CERTIFICADOS ATIVOS ===
    try {
        $sql = "SELECT COUNT(*) FROM {customcert_issues} ci
                JOIN {customcert} cc ON cc.id = ci.customcertid
                JOIN {course} c ON c.id = cc.course
                WHERE ci.userid = :userid AND c.format <> 'trails'";
        $debug_info['custom_active_count'] = $DB->get_field_sql($sql, ['userid' => $userid]);
    } catch (Exception $e) {
        $debug_info['custom_active_error'] = $e->getMessage();
    }

    // === RESULTADO DAS FUNÇÕES ===
    $debug_info['function_results'] = [];
    $debug_info['function_results']['simple_certificates'] = count(local_certificatepage_get_user_simple_certificates($userid));
    $debug_info['function_results']['custom_certificates'] = count(local_certificatepage_get_user_custom_certificates($userid));

    return $debug_info;
}

/**
 * Função temporária para testar certificados - REMOVER APÓS DEBUG
 * Adicione esta linha em qualquer página para testar:
 * echo '<pre>'; print_r(local_certificatepage_test_certificates($USER->id)); echo '</pre>';
 */
function local_certificatepage_test_certificates($userid) {
    global $DB;

    $result = [];
    $result['userid'] = $userid;

    // Testar certificados simples ativos
    $sql = "SELECT COUNT(*) FROM {simplecertificate_issues} si
            JOIN {simplecertificate} sc ON sc.id = si.certificateid
            JOIN {course} c ON c.id = sc.course
            WHERE si.userid = ? AND c.format <> 'trails'";
    $result['simple_active'] = $DB->count_records_sql($sql, [$userid]);

    // Testar certificados simples histórico
    if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_sc'))) {
        $sql = "SELECT COUNT(*) FROM {local_recertification_sc} WHERE userid = ?";
        $result['simple_history'] = $DB->count_records_sql($sql, [$userid]);
    } else {
        $result['simple_history'] = 'Tabela não existe';
    }

    // Testar certificados customcert ativos
    if ($DB->get_manager()->table_exists(new \xmldb_table('customcert_issues'))) {
        $sql = "SELECT COUNT(*) FROM {customcert_issues} ci
                JOIN {customcert} cc ON cc.id = ci.customcertid
                JOIN {course} c ON c.id = cc.course
                WHERE ci.userid = ? AND c.format <> 'trails'";
        $result['custom_active'] = $DB->count_records_sql($sql, [$userid]);
    } else {
        $result['custom_active'] = 'Tabela não existe';
    }

    // Testar certificados customcert histórico
    $table_name = local_certificatepage_get_customcert_table_name();
    if ($table_name && $DB->get_manager()->table_exists(new \xmldb_table($table_name))) {
        $sql = "SELECT COUNT(*) FROM {{$table_name}} WHERE userid = ?";
        $result['custom_history'] = $DB->count_records_sql($sql, [$userid]);
        $result['custom_table_used'] = $table_name;
    } else {
        $result['custom_history'] = 'Tabela não existe';
        $result['custom_table_used'] = $table_name ?: 'null';
    }

    // Resultado das funções
    $result['function_simple'] = count(local_certificatepage_get_user_simple_certificates($userid));
    $result['function_custom'] = count(local_certificatepage_get_user_custom_certificates($userid));

    return $result;
}

/**
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_linkedin_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql = <<<SQL

SELECT  issues.*,
        null cover,
        cm.id module,
        cm.course course
FROM {linkedin} cert

  JOIN {linkedin_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id
  JOIN {course} c
    ON c.id = cert.course

WHERE issues.userid = :userid
AND m.name = :name
AND c.format <> 'trails'
-- AND cm.visible = :visible
SQL;

    $param = [
        'userid'  => $userid,
        'name'    => 'linkedin',
        'visible' => 1
    ];

    $placeholder = "pix/cinza-placeholder.jpg";

    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates = $DB->get_records_sql($sql, $param)) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'linkedin';
        }
    }

    return $certificates;
}

/**
 * @param $course
 * @return null|string
 */
function local_certificatepage_get_course_image($course){
    global $CFG;

    if (!$course instanceof core_course_list_element) {
        $course = new \core_course_list_element($course);
    }

    $courseTrait = new class{
        use \local_courseblockapi\traits\course_trait;

        public function getCourseImage($course, $type) {
            return $this->get_course_image($course, "object");
        }
    };

    $courseimages = $courseTrait->getCourseImage($course, "object");

    if($courseimages && $courseimages->card){
        return $courseimages->card;
    }

    return null;
}

function local_certificatepage_get_default_course_image() {
    $fs = get_file_storage();

    // Returns an array of `stored_file` instances.
    $files = $fs->get_area_files(1, 'local_certificatepage', 'defaultcourseimage');

    foreach ($files as $file) {
        if($file->get_filename() === '.') continue;

        $url = moodle_url::make_pluginfile_url(
            $file->get_contextid(),
            $file->get_component(),
            $file->get_filearea(),
            $file->get_itemid(),
            $file->get_filepath(),
            $file->get_filename(),
            false                     // Do not force download of the file.
        );

        return $url;
    }

    return null;
}

/**
 * Buscar todos os certificados de trilhas do usuário (cursos com formato 'trails')
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_trail_certificates($userid)
{
    $simplecertificates = local_certificatepage_get_user_simple_trail_certificates($userid);
    $customcertificates = local_certificatepage_get_user_custom_trail_certificates($userid);

    $withlinkedinmodule = get_config('local_certificatepage', 'viewlinkedincertificates');

    if($withlinkedinmodule && has_mod_linkedin()) {
        $linkedincertificates = local_certificatepage_get_user_linkedin_trail_certificates($userid);
        $allusercertificates = array_merge($simplecertificates, $customcertificates, $linkedincertificates);
    } else {
        $allusercertificates = array_merge($simplecertificates, $customcertificates);
    }

    uasort($allusercertificates, function($certificate, $nextcertificate) {
        return $nextcertificate->timecreated > $certificate->timecreated;
    });

    return $allusercertificates;
}

/**
 * Buscar certificados SimpleCertificate de trilhas (cursos com formato 'trails')
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_simple_trail_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql_active = <<<SQL

SELECT  issues.*,
        cert.name as certificatename,
        c.fullname as coursename,
        null cover,
        cm.id module,
        cm.course course,
        'active' as source
FROM {simplecertificate} cert

  JOIN {simplecertificate_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course} c
    ON c.id = cert.course
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
AND c.format = 'trails'
-- AND cm.visible = :visible
SQL;

    $param_active = [
        'userid'  => $userid,
        'name'    => 'simplecertificate',
        'visible' => 1
    ];

    $certificates = [];

    if ($active_certificates = $DB->get_records_sql($sql_active, $param_active)) {
        $certificates = array_merge($certificates, $active_certificates);
    }

    $history_certificates = local_certificatepage_get_user_simple_trail_certificates_from_history($userid);
    if ($history_certificates) {
        $certificates = array_merge($certificates, $history_certificates);
    }

    $placeholder = "pix/cinza-placeholder.jpg";
    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'simplecertificate';
        }
    }

    return $certificates ?: [];
}

/**
 * Buscar certificados SimpleCertificate de trilhas do histórico de recertificação
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_simple_trail_certificates_from_history($userid)
{
    global $DB;

    if (!$DB->get_manager()->table_exists(new \xmldb_table('local_recertification_sc'))) {
        return [];
    }

    $sql = <<<SQL

SELECT  hist.*,
        hist.coursename,
        null cover,
        hist.courseid as course,
        hist.courseid as module,
        'history' as source
FROM {local_recertification_sc} hist
  JOIN {course} c
    ON c.id = hist.courseid

WHERE hist.userid = :userid
AND c.format = 'trails'
SQL;

    $param = [
        'userid' => $userid
    ];

    return $DB->get_records_sql($sql, $param) ?: [];
}

/**
 * Buscar certificados CustomCert de trilhas (cursos com formato 'trails')
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_custom_trail_certificates($userid)
{
    global $DB, $OUTPUT;

    // Verificar se o plugin customcert está instalado
    if (!$DB->record_exists('modules', ['name' => 'customcert', 'visible' => 1])) {
        return [];
    }

    $sql_active = <<<SQL

SELECT  issues.*,
        issues.customcertid as certificateid,
        cert.name as certificatename,
        c.fullname as coursename,
        null cover,
        cm.id module,
        cm.course course,
        'active' as source
FROM {customcert} cert

  JOIN {customcert_issues} issues
    ON cert.id = issues.customcertid
  JOIN {course} c
    ON c.id = cert.course
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id

WHERE issues.userid = :userid
AND m.name = :name
AND c.format = 'trails'
-- AND cm.visible = :visible
SQL;

    $param_active = [
        'userid'  => $userid,
        'name'    => 'customcert',
        'visible' => 1
    ];

    $certificates = [];

    if ($active_certificates = $DB->get_records_sql($sql_active, $param_active)) {
        $certificates = array_merge($certificates, $active_certificates);
    }

    $history_certificates = local_certificatepage_get_user_custom_trail_certificates_from_history($userid);
    if ($history_certificates) {
        $certificates = array_merge($certificates, $history_certificates);
    }

    $placeholder = "pix/cinza-placeholder.jpg";
    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'customcert';
        }
    }

    return $certificates ?: [];
}

/**
 * Buscar certificados CustomCert de trilhas do histórico de recertificação
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_custom_trail_certificates_from_history($userid)
{
    global $DB;

    $table_name = local_certificatepage_get_customcert_table_name();

    if (!$table_name || !$DB->get_manager()->table_exists(new \xmldb_table($table_name))) {
        return [];
    }

    try {
        $sql = <<<SQL

SELECT  hist.*,
        hist.customcertid as certificateid,
        hist.coursename,
        null as cover,
        hist.courseid as course,
        hist.courseid as module,
        'history' as source,
        'customcert' as modname
FROM {{$table_name}} hist
  JOIN {course} c
    ON c.id = hist.courseid

WHERE hist.userid = :userid
AND c.format = 'trails'
SQL;

        $param = [
            'userid' => $userid
        ];

        $results = $DB->get_records_sql($sql, $param);
        return $results ?: [];

    } catch (Exception $e) {
        error_log('Erro ao buscar certificados CustomCert de trilhas do histórico: ' . $e->getMessage());
        return [];
    }
}

/**
 * Buscar certificados LinkedIn de trilhas (cursos com formato 'trails')
 * @param $userid
 * @return array
 */
function local_certificatepage_get_user_linkedin_trail_certificates($userid)
{
    global $DB, $OUTPUT;

    $sql = <<<SQL

SELECT  issues.*,
        null cover,
        cm.id module,
        cm.course course
FROM {linkedin} cert

  JOIN {linkedin_issues} issues
    ON cert.id = issues.certificateid
  JOIN {course_modules} cm
    ON cm.course = cert.course
  JOIN {modules} m
    ON cm.module = m.id
  JOIN {course} c
    ON c.id = cert.course

WHERE issues.userid = :userid
AND m.name = :name
AND c.format = 'trails'
-- AND cm.visible = :visible
SQL;

    $param = [
        'userid'  => $userid,
        'name'    => 'linkedin',
        'visible' => 1
    ];

    $placeholder = "pix/cinza-placeholder.jpg";

    $image_url = local_certificatepage_get_default_course_image();

    if ($certificates = $DB->get_records_sql($sql, $param)) {
        foreach ($certificates as $certificate) {
            $course = \get_course($certificate->course);
            $certificate->cover = local_certificatepage_get_course_image($course, $placeholder) ?: $image_url ?: $OUTPUT->get_generated_image_for_id(\context_course::instance($course->id)->id);
            $certificate->defaultImage = empty(local_certificatepage_get_course_image($course, $placeholder));
            $certificate->modname = 'linkedin';
        }
    }

    return $certificates ?: [];
}

/**
 * Função de debug para certificados de trilhas - REMOVER APÓS CORREÇÃO
 * @param $userid
 * @return array
 */
function local_certificatepage_debug_trail_certificates($userid) {
    global $DB;

    $debug = [];
    $debug['userid'] = $userid;

    // Debug: Verificar se existem cursos com formato 'trails'
    $sql = "SELECT id, fullname, format FROM {course} WHERE format = 'trails'";
    $debug['trails_courses'] = $DB->get_records_sql($sql);
    $debug['trails_courses_count'] = count($debug['trails_courses']);

    // Debug: Verificar certificados CustomCert em cursos de trilhas
    if ($DB->record_exists('modules', ['name' => 'customcert', 'visible' => 1])) {
        $sql = "SELECT ci.*, cc.name as certname, c.fullname as coursename, c.format
                FROM {customcert_issues} ci
                JOIN {customcert} cc ON cc.id = ci.customcertid
                JOIN {course} c ON c.id = cc.course
                WHERE ci.userid = ? AND c.format = 'trails'";
        $debug['customcert_trails'] = $DB->get_records_sql($sql, [$userid]);
        $debug['customcert_trails_count'] = count($debug['customcert_trails']);
    } else {
        $debug['customcert_trails'] = 'Plugin CustomCert não instalado';
        $debug['customcert_trails_count'] = 0;
    }

    // Debug: Verificar certificados SimpleCertificate em cursos de trilhas
    if ($DB->record_exists('modules', ['name' => 'simplecertificate', 'visible' => 1])) {
        $sql = "SELECT si.*, sc.name as certname, c.fullname as coursename, c.format
                FROM {simplecertificate_issues} si
                JOIN {simplecertificate} sc ON sc.id = si.certificateid
                JOIN {course} c ON c.id = sc.course
                WHERE si.userid = ? AND c.format = 'trails'";
        $debug['simplecert_trails'] = $DB->get_records_sql($sql, [$userid]);
        $debug['simplecert_trails_count'] = count($debug['simplecert_trails']);
    } else {
        $debug['simplecert_trails'] = 'Plugin SimpleCertificate não instalado';
        $debug['simplecert_trails_count'] = 0;
    }

    // Debug: Testar as funções que criamos
    $debug['function_trail_certificates'] = count(local_certificatepage_get_user_trail_certificates($userid));
    $debug['function_custom_trail_certificates'] = count(local_certificatepage_get_user_custom_trail_certificates($userid));
    $debug['function_simple_trail_certificates'] = count(local_certificatepage_get_user_simple_trail_certificates($userid));

    return $debug;
}