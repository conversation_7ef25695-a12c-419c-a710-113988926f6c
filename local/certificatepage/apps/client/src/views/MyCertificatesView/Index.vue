<template>
  <div class="certificate-index">
    <h2 class="">Certificados</h2>

    <CustomNavbar />

    <!-- <div class="wrapper-filters d-flex justify-content-between align-items-md-end mb-2">
      <div class="d-flex flex-column flex-md-row w-100">
        <div class="filter-container">
          <custom-select
            label="Status"
            :options="statusOptions"
            v-model="selectedStatus"
            @change="handleFilterChange('status', $event)"
          >
          </custom-select>

          <custom-select
            label="Classificação"
            :options="classificationOptions"
            v-model="selectedClassification"
            @change="handleFilterChange('classification', $event)"
          >
          </custom-select>
        </div>
      </div>

      <div class="mt-3 flex-shrink-0">
        <button class="btn btn-primary" v-if="showCertificadoManager" @click="goToCertificadoManager()">
          Gerenciar Trilhas
        </button>
      </div>
    </div> -->

    <TransitionGroup class="row" name="fade" mode="out-in" tag="div">
      <!-- Cards de certificados -->
      <div v-for="certificado in this.certificados" :key="certificado.id"
        class="bubble col-12 col-sm-4 col-md-6 col-lg-4 col-xl-3">
        <div class="card">
          <div class="card-thumbnail">
            <img class="course-image" :class="{ 'default-image': certificado.defaultImage }" :src="certificado.cover"
              alt="">
            <div class="icon">
              <Medal />
            </div>
            <h5 v-if="certificado.defaultImage" class="card-title p-3 text-center">
              <a :href="handleCourseLink(certificado)" style="text-shadow: 0 1px #000;" title="Ir para o curso">{{
                certificado.coursename }}</a>
            </h5>
          </div>
          <div class="card-body">
            <a :href="handleCertificateDownload(certificado)" class="btn btn-primary button-download" :aria-label="'Baixar seu certificado ' + certificado.coursename">
              <i class="icomoon-cloud-download"></i>
              Baixar seu certificado
            </a>
          </div>
        </div>
      </div>
      <!-- <div class="alert alert-info" role="alert">
      <strong>Ops!</strong> Você ainda não possui certificados.
      </div> -->

    </TransitionGroup>

    <LFLoading :isLoading="isLoading" />

    <div class="mt-4">
      <EmptyList :show="!certificados.length && !isLoading" text="Nenhum certificado" />
    </div>

    <BootstrapPagination class="pt-3" v-show="total" :currentPage="currentPage" :totalPages="totalPages"
      @page-changed="setPage" />
  </div>
</template>

<script>
import _ from 'lodash'
import Config from 'core/config'
import { getUser } from 'tool_lfxp/user'
import { ajax } from '@/helpers/moodle'
import EmptyList from '@/components/EmptyList.vue'
import LFLoading from '@/components/LFLoading.vue'
import CustomSelect from '@/components/CustomSelect.vue'
import BootstrapPagination from '@/components/BootstrapPagination.vue'
import CustomNavbar from '@/components/CustomNavbar.vue'
import Medal from '@/assets/icons/Medal.svg'

export default {
  name: 'MyCertificatesView',

  components: {
    EmptyList,
    LFLoading,
    CustomSelect,
    BootstrapPagination,
    CustomNavbar,
    Medal
  },

  data() {
    return {
      isInitialLoad: true,
      User: {},

      certificados: [],
      isLoading: false,
      search: '',

      total: 0,
      perPage: 8,
      currentPage: 1,
      totalPages: 0,

    }
  },

  computed: {

  },

  async created() {
    this.getCertificados()

    this.User = await getUser()


  },

  watch: {
    currentPage() {
      this.getCertificados()
    }
  },

  methods: {
    async getCertificados() {
      try {

        this.isLoading = true
        this.certificados = []

        const response = await ajax('local_certificatepage_list_user_certificates', {
          filters: [],
          per_page: this.perPage,
          current_page: this.currentPage
        })
        //console.log("Meus certificados: ", response)
        this.certificados = response.certificates
        this.total = response.total
        this.totalPages = response.total_pages
      } catch (error) {
        console.error('Error fetching certificados:', error)
      } finally {
        this.isLoading = false
        this.isInitialLoad = false
      }
    },

    setPage(page) {
      if (this.currentPage === page) return

      if (page < 1 || page > this.totalPages) return

      this.currentPage = page
    },

    // handleFilterChange(filterType, value) {
    //   // Atualiza o objeto de filtros diretamente
    //   this.filters[filterType] = value

    //   if (!this.isInitialLoad) {
    //     // Chama getCertificados com os filtros atualizados
    //     this.getCertificados()
    //   }
    // },

    handleCertificateDownload(certificado) {
      return `${Config.wwwroot}/mod/${certificado.modname}/wmsendfile.php?code=${certificado.code}`
    },

    handleCourseLink(certificado) {
      return `${Config.wwwroot}/course/view.php?id=${certificado.course}`
    }
  }
}
</script>

<style lang="scss" scoped>
.certificate-index {
  .bubble {
    margin-bottom: 30px !important;
  }

  .icomoon-cloud-download {
    color: #fff;
    font-size: 20px !important;
    display: flex !important;
    align-items: center;
    margin-right: 10px;
  }

  .card-title a {
    color: #878787 !important;
  }

  .course-certificate>a:link {
    text-decoration: none;
  }

  .card img {
    max-width: 100%;
    max-height: 150px;
  }

  .button-download {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100% !important;
  }

  .button-download a {
    color: #fff;
  }

  .button-download a:hover {
    color: #fff;
  }

  .bubble .card {
    width: 100%;
    max-width: 362px;
    height: 100%;
  }


  .bubble .card .card-thumbnail {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }

  .bubble .card .card-thumbnail .course-image {
    max-height: 226px;
    min-height: 198px;
    width: 100%;
    height: 100%;
  }

  .bubble .card .card-thumbnail .course-image.default-image {
    filter: brightness(0.85);
  }

  .bubble .card .card-thumbnail .icon {
    position: absolute;
    width: 34px;
    height: 50px;
    left: 15px;
    top: 15px;
    margin: 0;
    filter: unset;
  }

  .bubble .card .card-title {
    position: absolute;
    margin: 0;
  }

  .bubble .card .card-title a {
    color: #fff !important;
    font-weight: bold;
    font-size: 20px;
    text-transform: uppercase;
  }



  .bubble .card .card-body {
    flex: unset;
  }

  .wrapper-filters {
    @media(max-width: 768px) {
      flex-direction: column;
    }
  }

  .filter-container {
    display: flex;
    gap: 12px;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }

}
</style>
