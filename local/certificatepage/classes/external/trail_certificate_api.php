<?php

namespace local_certificatepage\external;

use \coding_exception;
use \core_external\external_api;
use \core_external\external_warnings;
use \core_external\external_value;
use \core_external\external_single_structure;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;

defined('MOODLE_INTERNAL') || die();

//require(__DIR__ . '/../../../../config.php');
require_once(__DIR__ . '/../../../trails/lib.php');
require_once($CFG->libdir . "/externallib.php");


class trail_certificate_api extends external_api
{

    const DEFAULT_PAGE_SIZE = 8;

    public static function list_user_trail_certificates_is_allowed_from_ajax()
    {
        return true;
    }

    public static function list_user_trail_certificates_parameters()
    {
        return new external_function_parameters([
            'filters' => new external_single_structure([
                // 'search' => new external_value(PARAM_TEXT, 'Keyword to search for trails', VALUE_OPTIONAL),
                'status' => new external_value(PARAM_INT, 'User progress status', VALUE_DEFAULT, -1),
                'classification' => new external_value(PARAM_INT, 'Certificate classification for user', VALUE_OPTIONAL),
            ]),
            'current_page' => new external_value(PARAM_INT, 'Current page', VALUE_DEFAULT, 0),
            'per_page' => new external_value(PARAM_INT, 'Certificates per page', VALUE_DEFAULT, self::DEFAULT_PAGE_SIZE),
        ]);
    }

    public static function list_user_trail_certificates_returns()
    {
        return new external_single_structure([
            'per_page'     => new external_value(PARAM_INT, 'Certificates per page', VALUE_REQUIRED),
            'current_page' => new external_value(PARAM_INT, 'Current page', VALUE_OPTIONAL),
            'total'        => new external_value(PARAM_INT, 'Certificates total', VALUE_OPTIONAL),
            'total_pages'  => new external_value(PARAM_INT, 'Total number of pages', VALUE_OPTIONAL),
            'certificates' => new external_multiple_structure(
                new external_single_structure([
                    'certificatename' => new external_value(PARAM_TEXT, 'Certificate Name', VALUE_OPTIONAL),
                    'trailname' => new external_value(PARAM_TEXT, 'Trail Name', VALUE_OPTIONAL),
                    'userid' => new external_value(PARAM_INT, 'User ID', VALUE_OPTIONAL),
                    'code' => new external_value(PARAM_TEXT, 'Certificate Code', VALUE_OPTIONAL),
                    'download_url' => new external_value(PARAM_TEXT, 'Download URL', VALUE_OPTIONAL),
                    'linkedin_url' => new external_value(PARAM_TEXT, 'LinkedIn URL', VALUE_OPTIONAL),
                    'image_url' => new external_value(PARAM_TEXT, 'Image URL', VALUE_OPTIONAL),
                    'timeissued' => new external_value(PARAM_INT, 'Time Issued', VALUE_OPTIONAL),
                ])
            )
        ]);
    }

    public static function list_user_trail_certificates($filters = [], $current_page = 1, $per_page = self::DEFAULT_PAGE_SIZE)
    {
        global $DB, $PAGE, $USER;


        $response = [
            'certificates' => [],
            'per_page' => $per_page,
            'current_page' => $current_page,
            'total' => 0,
            'total_pages' => 0,
        ];

        $params = self::validate_parameters(self::list_user_trail_certificates_parameters(), [
            'filters' => $filters,
            'current_page' => $current_page,
            'per_page' => $per_page,
        ]);

        $filters = $params['filters'];

        if ($filters['status'] < 0) {
            unset($filters['status']);
        }

        // Obtenha o contexto do usuário logado
        $context = \context_user::instance($USER->id);

        // Defina o contexto da página
        $PAGE->set_context($context);

        // Chamada para o método local_certificatepage_get_user_trail_certificates
        $certificates = local_certificatepage_get_user_trail_certificates($USER->id);

        // Filtrar por modname se especificado
        if (!empty($filters['modname'])) {
            $certificates = array_filter($certificates, function($cert) use ($filters) {
                return $cert->modname === $filters['modname'];
            });
        }

        // Verificando se há certificados retornados
        if (!empty($certificates)) {
            $response['total'] = count($certificates);

            // Paginação
            $total_pages = ceil($response['total'] / $per_page);

            // Aplicar paginação
            $start = ($current_page - 1) * $per_page;
            $certificates = array_slice($certificates, $start, $per_page);

            // Convertendo o array associativo para um array simples
            $certificates = array_values($certificates);

            // Formatando os certificados para o formato de retorno da API
            foreach ($certificates as $certificate) {
                $formatted_certificate = [
                    'id' => $certificate->id,
                    'certificatename' => $certificate->certificatename ?? $certificate->name ?? 'Certificado',
                    'coursename' => $certificate->coursename,
                    'userid' => $certificate->userid,
                    'code' => $certificate->code,
                    'modname' => $certificate->modname,
                    'source' => $certificate->source ?? 'active',
                    'timecreated' => $certificate->timecreated,
                    'cover' => $certificate->cover,
                    'defaultImage' => $certificate->defaultImage ?? false,
                    'course' => $certificate->course,
                    'module' => $certificate->module,
                    'certificateid' => $certificate->certificateid ?? null,
                    'fileurl' => $certificate->fileurl ?? null,
                ];
                $response['certificates'][] = $formatted_certificate;
            }

            $response['total_pages'] = $total_pages;
        }

        return $response;
    }
}
