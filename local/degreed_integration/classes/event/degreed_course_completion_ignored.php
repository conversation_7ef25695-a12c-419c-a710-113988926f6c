<?php namespace local_degreed_integration\event;

defined('MOODLE_INTERNAL') || die();

use \core\event\base;
class degreed_course_completion_ignored extends base {

    /**
     * Initialise the event data.
     */
    protected function init() {
        $this->data['objecttable'] = 'course_completions';
        $this->data['crud'] = 'r';
        $this->data['edulevel'] = self::LEVEL_OTHER;
    }

    /**
     * Returns localised general event name.
     *
     * @return string
     */
    public static function get_name() {
        return get_string('event:degreed_course_completion_ignored', 'local_degreed_integration');
    }

    /**
     * Returns non-localised description of what happened.
     *
     * @return string
     */
    public function get_description() {
        return "Course completion ignored by Degreed integration";
    }


    public static function create_from_course_completion(object $completion, string $reason){
        $event = static::create([
            'objectid' => $completion->id,
                'relateduserid' => $completion->userid,
                'context' => \context_course::instance($completion->course),
                'courseid' => $completion->course,
            'other' => [
                'reason' => $reason,
            ],
        ]);

        return $event;
    }
}