<?php

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Degreed integration';
$string['settings:manage_title'] = 'Manage Degreed integration';
$string['settings:enable'] = 'Enable';
$string['settings:enable_desc'] = '';
$string['settings:api_base_url'] = 'Base API URL';
$string['settings:api_base_url_desc'] = '';
$string['settings:api_token_url'] = 'Login URL (API)';
$string['settings:api_token_url_desc'] = '';
$string['settings:client_id'] = 'Client ID';
$string['settings:client_id_desc'] = '';
$string['settings:client_secret'] = 'Client Secret';
$string['settings:client_secret_desc'] = '';

$string['exception:unable_to_get_access_token'] = 'Unable to login on Degreed\'s API';
$string['exception:unauthorized_on_api'] = 'Unauthorized on Degreed\'s API';



// Automatically added strings
$string['exception:course_not_found'] = 'Course not found!';
$string['exception:error_getting_course'] = 'Error fetching course!';
$string['exception:error_creating_course'] = 'Error creating course!';
$string['exception:error_updating_course'] = 'Error updating course!';
$string['exception:error_upserting_course_skills'] = 'Error updating course skills!';
$string['exception:course_completion_not_found'] = 'Course completion not found!';
$string['exception:error_getting_course_completion'] = 'Error fetching course completion!';
$string['exception:error_creating_course_completion'] = 'Error creating course completion!'; $string['exception:error_updating_course_completion'] = 'Error updating course completion!';
$string['exception:missing_course'] = 'Course not found';
$string['event:degreed_course_completion_created'] = 'Course completion created in Degreed';
$string['event:degreed_course_completion_updated'] = 'Course completion updated in Degreed';
$string['event:degreed_course_created'] = 'Course created in Degreed';
$string['event:degreed_course_updated'] = 'Course updated in Degreed';
$string['task:sync_task'] = 'Synchronization task with Degreed';

$string['event:degreed_course_completion_ignored'] = "Course completion ignored by Degreed integration";