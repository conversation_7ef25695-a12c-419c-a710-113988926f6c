<template>
  <div v-if="show" class="modal-backdrop" @click="closeOnBackdrop ? $emit('close') : null">
    <div class="modal-container" :class="[`modal-${size}`]" @click.stop>
      <!-- Cabeçalho do modal -->
      <div class="modal-header">
        <h3 class="modal-title">{{ title }}</h3>
        <button class="modal-close" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Corpo do modal -->
      <div class="modal-body">
        <!-- Loading overlay -->
        <div v-if="isSubmitting" class="loading-overlay">
          <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Carregando...</span>
            </div>
            <p class="loading-text mt-3">Processando matrículas...</p>
          </div>
        </div>

        <!-- <PERSON><PERSON><PERSON><PERSON> de alerts após submissão -->
        <div v-if="showResultAlerts" class="result-alerts">
          <div v-if="batchMessage" class="alert" :class="batchMessageType === 'success' ? 'alert-success' : 'alert-danger'">
            <i :class="batchMessageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
            {{ batchMessage }}
          </div>
          <div v-if="failedMessages.length > 0" class="failed-messages">
            <div v-for="(message, index) in failedMessages" :key="index" class="alert alert-warning">
              <i class="fas fa-exclamation-triangle"></i>
              {{ message }}
            </div>
          </div>
          <div v-if="reenrolMessages.length > 0" class="reenrol-messages">
            <div v-for="(message, index) in reenrolMessages" :key="index" class="alert alert-info">
              <i class="fas fa-exclamation-triangle"></i>
              {{ message }}
            </div>
          </div>
        </div>

        <!-- Conteúdo normal do modal -->
        <div v-else class="enrolment-modal">
          <h3 class="section-title">OPÇÕES DE MATRÍCULA</h3>

          <div class="form-row">
            <div class="form-group">
              <div class="label-with-help">
                <label class="form-label">Forma de matrícula</label>
                <i class="fas fa-exclamation-circle required-icon" title="Campo obrigatório"></i>
              </div>
              <div class="limited-width-input">
                <CustomSelect v-model="enrolmentMethod" :options="enrolmentMethodOptions" style="width: 100%"
                  required />
              </div>
            </div>

            <div class="form-group">
              <div class="label-with-help">
                <label class="form-label">Papel para atribuir</label>
                <i class="fas fa-exclamation-circle required-icon" title="Campo obrigatório"></i>
              </div>
              <div class="limited-width-input">
                <CustomSelect v-model="selectedRoleId" :options="roles" :modelValue="selectedRoleId" class="w-100" required />
                <div v-if="roles.length === 0" class="error-message">
                  Não foi possível carregar os papéis disponíveis para esta
                  turma.
                </div>
              </div>
            </div>
          </div>

          <!-- Seleção manual de usuários -->
          <div v-if="enrolmentMethod === 'manual'" class="form-group">
            <div class="label-with-help">
              <label class="form-label">Selecionar usuários</label>
              <i class="fas fa-exclamation-circle required-icon" title="Campo obrigatório"></i>
            </div>
            <div class="user-select-container">
              <div class="custom-autocomplete-wrapper">
                <input type="text" class="form-control" placeholder="Buscar..." v-model="searchQuery"
                  @input="handleInput" />
                <div class="select-arrow"></div>

                <div v-if="isOpen" class="dropdown-menu show">
                  <div v-for="(user, index) in userOptions" :key="user.value" class="dropdown-item" @click="selectUser(user)">
                    {{ user.label }}
                    <i v-if="selectedUsers.some((u) => u.value === user.value)" class="fas fa-check"></i>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="selectedUsers.length > 0" class="selected-users-container">
              <div class="filter-tags">
                <div v-for="user in selectedUsers" :key="user.value" class="tag badge badge-primary"
                  @click="removeUser(user)">
                  <i class="fas fa-times"></i>
                  {{ user.label }}
                </div>
              </div>
            </div>
          </div>

          <!-- Upload de arquivo CSV para matrícula em lote -->
          <div v-if="enrolmentMethod === 'batch'" class="form-group">
            <div class="label-with-help">
              <label class="form-label">Matricular usuários a partir de um arquivo CSV</label>
              <i class="fas fa-exclamation-circle required-icon" title="Campo obrigatório"></i>
            </div>

            <div class="csv-upload-area" @dragover.prevent="onDragOver" @dragleave.prevent="onDragLeave"
              @drop.prevent="onDrop" @click="$refs.fileInput.click()" :class="{ 'drag-over': isDragging }">
              <input type="file" ref="fileInput" accept=".csv" style="display: none" @change="handleFileSelect" />

              <!-- Conteúdo quando não há arquivo selecionado -->
              <template v-if="!selectedFile">
                <div class="upload-icon">
                  <i class="fas fa-arrow-down"></i>
                </div>
                <p class="upload-text">
                  Você pode arrastar e soltar arquivos aqui para adicioná-los.
                </p>
              </template>

              <!-- Conteúdo quando há arquivo selecionado -->
              <template v-else>
                <div class="file-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <p class="file-name">{{ selectedFile.name }}</p>
                <p class="file-size">
                  ({{ formatFileSize(selectedFile.size) }})
                </p>
                <p class="file-replace-text">
                  Clique ou arraste outro arquivo para substituir
                </p>
              </template>
            </div>

            <div v-if="csvUsers.length > 0" class="csv-users-preview">
              <div class="preview-header">
                <span>Usuários encontrados no arquivo ({{
                  csvUsers.length
                }}):</span>
              </div>
              <div class="selected-users-container">
                <div class="filter-tags">
                  <div v-for="user in csvUsers.slice(0, 5)" :key="user.id" class="tag badge badge-primary">
                    {{ user.name }}
                  </div>
                  <span v-if="csvUsers.length > 5" class="more-users">+{{ csvUsers.length - 5 }} mais</span>
                </div>
              </div>
            </div>

            <div class="csv-info">
              <p class="csv-format-text">Formatos aceitos: CSV</p>

              <div class="csv-example">
                <span class="example-label">Exemplo CSV</span>
                <a :href="`/local/offermanager/export_potential_users.php?offerclassid=${offerclassid}`"
                  class="example-csv">example.csv</a>
              </div>

              <div class="csv-options-row">
                <div class="csv-option">
                  <label>Delimitador do CSV</label>
                  <CustomSelect v-model="csvDelimiter" :options="delimiterOptions" :width="160" />
                </div>
                <div class="csv-option">
                  <label>Codificação</label>
                  <CustomSelect v-model="csvEncoding" :options="encodingOptions" :width="160" />
                </div>
              </div>
            </div>
          </div>

          <div class="form-info">
            <span style="color: #f8f9fa; font-size: 15px">Este formulário contém campos obrigatórios marcados com</span>
            <i class="fas fa-exclamation-circle" style="color: #dc3545; font-size: 0.85rem; vertical-align: middle"></i>
          </div>
        </div>
      </div>

      <!-- Rodapé do modal -->
      <div class="modal-footer" v-if="!showResultAlerts">
        <button class="btn btn-primary" @click="handleSubmit" :disabled="isSubmitting || !isFormValid">
          {{ confirmButtonText }}
        </button>
        <button class="btn btn-secondary" @click="$emit('close')">
          {{ cancelButtonText }}
        </button>
      </div>
    </div>
  </div>

  <Toast :show="showToast" :message="toastMessage" :type="toastType" :duration="3000" />
</template>

<script>

import CustomSelect from "./CustomSelect.vue";
import {enrolUsers, getPotentialUsersToEnrol} from "@/services/enrolment";
import Toast from "@/components/Toast.vue";
import { getClass, getCourseRoles } from "@/services/offer";

export default {
  name: "EnrolmentModalNew",

  components: {
    Toast,
    CustomSelect,
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "Matricular usuários na turma",
    },
    size: {
      type: String,
      default: "lg",
      validator: (value) => ["sm", "md", "lg", "xl"].includes(value),
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true,
    },
    confirmButtonText: {
      type: String,
      default: "Salvar",
    },
    cancelButtonText: {
      type: String,
      default: "Cancelar",
    },
    offerclassid: {
      type: Number,
      required: true,
    },
    roles: {
      type: Array,
      required: true,
    },
  },

  emits: ["close", "success"],

  data() {
    return {
      // Método de matrícula
      
      enrolmentMethod: "manual",
      enrolmentMethodOptions: [
        { value: "manual", label: "Manual" },
        { value: "batch", label: "Em lote" },
      ],

      // Papel selecionado
      selectedRoleId: "",

      // Usuários
      searchQuery: "",
      isOpen: false,
      userOptions: [],
      selectedUsers: [],
      debounceTimer: null,

      // CSV Upload
      selectedFile: null,
      csvUsers: [],
      isDragging: false,
      csvDelimiter: ",",
      csvEncoding: "UTF-8",
      delimiterOptions: [
        { value: ",", label: "," },
        { value: ";", label: ";" },
        { value: ":", label: ":" },
        { value: "\t", label: "\\t" },
        { value: " ", label: "Espaço" },
      ],
      encodingOptions: [
        { value: "UTF-8", label: "UTF-8" },
        { value: "WINDOWS-1252", label: "WINDOWS-1252" },
        { value: "ISO-8859-1", label: "ISO-8859-1" },
        { value: "ASCII", label: "ASCII" },
        { value: "ISO-8859-2", label: "ISO-8859-2" },
        { value: "ISO-8859-3", label: "ISO-8859-3" },
        { value: "ISO-8859-4", label: "ISO-8859-4" },
        { value: "ISO-8859-5", label: "ISO-8859-5" },
        { value: "ISO-8859-6", label: "ISO-8859-6" },
        { value: "ISO-8859-7", label: "ISO-8859-7" },
        { value: "ISO-8859-8", label: "ISO-8859-8" },
        { value: "ISO-8859-9", label: "ISO-8859-9" },
        { value: "ISO-8859-10", label: "ISO-8859-10" },
        { value: "ISO-8859-13", label: "ISO-8859-13" },
        { value: "ISO-8859-14", label: "ISO-8859-14" },
        { value: "ISO-8859-15", label: "ISO-8859-15" },
        { value: "ISO-8859-16", label: "ISO-8859-16" },
        { value: "WINDOWS-874", label: "WINDOWS-874" },
        { value: "WINDOWS-1250", label: "WINDOWS-1250" },
        { value: "WINDOWS-1251", label: "WINDOWS-1251" },
        { value: "WINDOWS-1253", label: "WINDOWS-1253" },
        { value: "WINDOWS-1254", label: "WINDOWS-1254" },
        { value: "WINDOWS-1255", label: "WINDOWS-1255" },
        { value: "WINDOWS-1256", label: "WINDOWS-1256" },
        { value: "WINDOWS-1257", label: "WINDOWS-1257" },
        { value: "WINDOWS-1258", label: "WINDOWS-1258" },
        { value: "KOI8-R", label: "KOI8-R" },
        { value: "MACINTOSH", label: "MACINTOSH" },
        { value: "IBM866", label: "IBM866" },
        { value: "BIG5", label: "BIG5" },
        { value: "EUC-JP", label: "EUC-JP" },
        { value: "SHIFT_JIS", label: "SHIFT_JIS" },
        { value: "EUC-KR", label: "EUC-KR" },
        { value: "UTF-7", label: "UTF-7" },
        { value: "UTF-16", label: "UTF-16" },
        { value: "UTF-32", label: "UTF-32" },
        { value: "UCS-2", label: "UCS-2" },
        { value: "UCS-4", label: "UCS-4" },
      ],

      loadingUsers: false,
      isSubmitting: false,

      // Toast
      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,

      showResultAlerts: false,
      batchMessage: "",
      batchMessageType: "success",
      failedMessages: [],
      reenrolMessages: []
    };
  },

  computed: {
    isFormValid() {
      if (this.roles.length === 0) {
        return false;
      }

      if (this.enrolmentMethod === "manual") {
        return this.selectedUsers.length > 0 && this.selectedRoleId;
      } else if (this.enrolmentMethod === "batch") {
        return this.csvUsers.length > 0 && this.selectedRoleId;
      }
      return false;
    },
  },

  watch: {
    show(newVal) {
      document.body.style.overflow = newVal ? "hidden" : "";

      if (newVal) {
        this.initializeForm();
      }
    },
  },

  mounted() {
    document.addEventListener("keydown", this.handleKeyDown);
    document.addEventListener("click", this.handleClickOutside);

    if (this.show) {
      document.body.style.overflow = "hidden";
      this.initializeForm();
    }
  },

  unmounted() {
    document.removeEventListener("keydown", this.handleKeyDown);
    document.removeEventListener("click", this.handleClickOutside);
    document.body.style.overflow = "";
  },

  methods: {
    handleKeyDown(e) {
      if (this.show && e.key === "Escape") {
        this.$emit("close");
      }
    },

    handleClickOutside(event) {
      if (this.show === false) return;

      const autocompleteEl = document.querySelector(
        ".custom-autocomplete-wrapper"
      );
      if (autocompleteEl && !autocompleteEl.contains(event.target)) {
        this.isOpen = false;
      }
    },

    async initializeForm() {

      this.resetForm();
    },

    resetForm() {
      let studentRole = this.roles.find((role) => role.value == 5);
      this.enrolmentMethod = "manual";
      this.selectedRoleId = studentRole.value;
      this.searchQuery = "";
      this.selectedUsers = [];
      this.selectedFile = null;
      this.csvUsers = [];
      this.csvDelimiter = ",";
      this.csvEncoding = "UTF-8";

      this.showResultAlerts = false;
      this.batchMessage = "";
      this.batchMessageType = "success";
      this.failedMessages = [];
      this.reenrolMessages = [];
    },

    async fetchPotentialUsersToEnrol(term) {
      this.loadingUsers = true;

      let excludedUserids = this.selectedUsers.map(user => user.value);

      const response = await getPotentialUsersToEnrol(
        this.offerclassid,
        term,
        excludedUserids
      );

      this.userOptions = response.data.map((user) => ({
        value: user.id,
        label: user.fullname,
      }));
  
      this.loadingUsers = false;
    },

    handleInput() {
      const term = this.searchQuery.trim();

      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      if (term.length >= 3) {
        this.debounceTimer = setTimeout(async () => {
          await this.fetchPotentialUsersToEnrol(term);
          if(this.userOptions) {
            this.isOpen = true;
          }
        }, 500);
      } else {
        this.isOpen = false;
        this.userOptions = [];
      }
    },

    selectUser(user) {
      // Verificar se o usuário já está selecionado
      const index = this.selectedUsers.findIndex((u) => u.value === user.value);

      if (index === -1) {
        // Adicionar usuário se não estiver selecionado
        this.selectedUsers.push(user);
      } else {
        // Remover usuário se já estiver selecionado
        this.selectedUsers.splice(index, 1);
      }

      this.searchQuery = "";
      this.isOpen = false;
    },

    removeUser(user) {
      this.selectedUsers = this.selectedUsers.filter(
        (u) => u.value !== user.value
      );
    },

    // Métodos para upload de CSV
    onDragOver() {
      this.isDragging = true;
    },

    onDragLeave() {
      this.isDragging = false;
    },

    onDrop(event) {
      this.isDragging = false;
      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.processFile(files[0]);
      }
    },

    handleFileSelect(event) {
      const files = event.target.files;
      if (files.length > 0) {
        this.processFile(files[0]);
      }
    },

    removeFile() {
      this.selectedFile = null;
      this.csvUsers = [];
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = "";
      }
    },

    processFile(file) {
      if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
        this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");
        return;
      }

      this.selectedFile = file;
      this.readCSVFile(file);
    },

    readCSVFile(file) {
      const reader = new FileReader();

      reader.onload = (e) => {
        const content = e.target.result;
        this.parseCSV(content);
      };

      reader.onerror = (e) => {
        console.error("Erro ao ler o arquivo:", e);

        // Tentar novamente com UTF-8 se a codificação selecionada falhar
        if (this.csvEncoding !== "UTF-8") {
          console.log("Tentando ler com UTF-8 como fallback...");
          const fallbackReader = new FileReader();
          fallbackReader.onload = (e) => {
            const content = e.target.result;
            this.parseCSV(content);
          };
          fallbackReader.onerror = () => {
            this.showErrorMessage(
              "Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos."
            );
          };
          fallbackReader.readAsText(file, "UTF-8");
        } else {
          this.showErrorMessage(
            "Não foi possível ler o arquivo. Verifique se o formato está correto."
          );
        }
      };

      try {
        reader.readAsText(file, this.csvEncoding);
      } catch (error) {
        console.error(
          "Erro ao tentar ler o arquivo com a codificação selecionada:",
          error
        );
        this.showErrorMessage(
          `Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`
        );
      }
    },

    parseCSV(content) {
      try {
        const delimiter = this.csvDelimiter;

        // Verificar se o conteúdo tem caracteres inválidos devido a problemas de codificação
        const hasInvalidChars = /�/.test(content);
        if (hasInvalidChars) {
          console.warn(
            "O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada."
          );
        }

        const lines = content.split(/\r?\n/);
        const users = [];

        if (lines.length < 2) {
          console.log("EnrolmentModalNew - Linhas do CSV:", lines);
          this.showErrorMessage(
            "O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados."
          );
          return;
        }

        // Função auxiliar para dividir a linha com base no delimitador
        const splitLine = (line, delim) => {
          // Tratamento especial para delimitadores que precisam de escape em expressões regulares
          if (delim === "\\t") {
            return line.split("\t");
          } else if (delim === " ") {
            // Para espaços, podemos ter múltiplos espaços consecutivos
            return line.split(/\s+/);
          } else {
            // Para outros delimitadores como vírgula, ponto e vírgula, dois pontos
            // Escapar caracteres especiais em expressões regulares
            const escapedDelim = delim.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
            return line.split(new RegExp(escapedDelim));
          }
        };

        // Verificar o cabeçalho
        const header = splitLine(lines[0].toLowerCase(), delimiter);
        if (
          header.length < 2 ||
          !header.some((h) => h.includes("userid")) ||
          !header.some((h) => h.includes("firstname"))
        ) {
          this.showErrorMessage(
            "O arquivo CSV deve conter colunas para UserID e firstname do usuário."
          );
          return;
        }

        // Encontrar os índices das colunas de UserID e firstname
        const idIndex = header.findIndex((h) => h.includes("userid"));
        const nameIndex = header.findIndex((h) => h.includes("firstname"));

        // Ignorar a primeira linha (cabeçalho)
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const columns = splitLine(line, delimiter);

          // Verificar se temos colunas suficientes
          if (columns.length > Math.max(idIndex, nameIndex)) {
            const id = columns[idIndex].trim();
            const name = columns[nameIndex].trim();

            if (id && name) {
              // Verificar se o ID é um número válido
              if (!/^\d+$/.test(id)) {
                console.warn(
                  `Linha ${i + 1}: ID inválido '${id}'. Deve ser um número.`
                );
                continue;
              }

              users.push({ id, name });
            }
          }
        }

        if (users.length === 0) {
          if (hasInvalidChars) {
            this.showErrorMessage(
              "Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."
            );
          } else {
            this.showErrorMessage(
              "Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo."
            );
          }
          return;
        }

        this.csvUsers = users;
      } catch (error) {
        console.error("Erro ao processar arquivo CSV:", error);
        this.showErrorMessage(
          "Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente."
        );
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";

      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      // Arredondar para 2 casas decimais e remover zeros à direita
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },

    async handleSubmit() {
      if (!this.isFormValid) return;

      try {
        this.isSubmitting = true;

        let userIds = [];

        if (this.enrolmentMethod === "manual") {
          userIds = this.selectedUsers.map((user) => user.value);
        } else if (this.enrolmentMethod === "batch") {
          userIds = this.csvUsers.map((user) => parseInt(user.id));
        }

        if(!userIds) {
          this.showErrorMessage('Nenhum usuário selecionado para efetuar a matrícula');
        }

        const response = await enrolUsers({
          offerclassid: this.offerclassid,
          userids: userIds,
          roleid: parseInt(this.selectedRoleId),
        });

        if(response.data) {
          this.showResultAlerts = true;

          const successEnrolments = response.data.filter((result) => result.success);
          const successCount = successEnrolments.length;
          const reenrolEnrolments = successCount > 0 ? successEnrolments.filter((result) => result.reenrol) : [];
          const failedEnrolments = response.data.filter((result) => result.success == false);

          this.batchMessage = successCount > 0 
            ? `${successCount} de ${userIds.length} usuário(s) matriculado(s) com sucesso.` 
            : 'Nenhuma inscrição foi realizada'
          ;
      
          this.batchMessageType = successCount > 0 ? 'success' : 'danger';

          this.reenrolMessages = reenrolEnrolments.length > 0 
            ? reenrolEnrolments.map(enrolment => enrolment.message) 
            : []
          ;

          this.failedMessages = failedEnrolments.length > 0 
            ? failedEnrolments.map(enrolment => enrolment.message) 
            : []
          ;
          
          if (successCount > 0) {
            this.$emit("success", {
              count: successCount,
              total: userIds.length,
            });
          }
        }
      } catch (error) {
        this.showErrorMessage(error.message || "Erro ao matricular usuários. Tente novamente.");
      } finally {
        this.isSubmitting = false;
      }
    },

    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      // Isso ajuda a reiniciar a animação corretamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000); // Usar a duração definida no componente Toast se necessário
      });
    },

    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #212529;
  border-radius: 6px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  max-width: 500px;
  border: 1px solid #373b3e;
}

.modal-sm {
  max-width: 300px;
}

.modal-md {
  max-width: 400px;
}

.modal-lg {
  max-width: 500px;
}

.modal-xl {
  max-width: 1140px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #373b3e;
  background-color: #212529;
  position: relative;
  /* Para posicionamento absoluto do botão de fechar */

  .modal-title {
    margin: 0;
    font-size: 18.75px;
    /* Tamanho exato de 18.75px conforme solicitado */
    font-weight: bold;
    color: #f8f9fa;
    /* Cor #F8F9FA conforme solicitado */
  }

  .modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1rem;
    /* Tamanho reduzido */
    cursor: pointer;
    padding: 0;
    line-height: 1;
    width: 20px;
    /* Largura fixa menor */
    height: 20px;
    /* Altura fixa menor */
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);

    i {
      font-size: 0.9rem;
      /* Tamanho do ícone reduzido */
    }

    &:hover {
      color: #ffffff;
      opacity: 0.8;
    }
  }
}

.modal-body {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
  position: relative;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem 1rem;
  border-top: 1px solid #373b3e;
  gap: 0.5rem;
  background-color: #212529;

  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 15px;
    line-height: 1.5;
    border-radius: 4px;
    cursor: pointer;

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }
}

// Loading overlay styles
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 0 0 6px 6px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-text {
  color: #f8f9fa;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

// Bootstrap spinner override for better visibility
.spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3em;
}

.text-primary {
  color: var(--primary) !important;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.result-alerts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;

  .failed-messages {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}

// Estilos para o conteúdo do modal de matrícula
.enrolment-modal {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-title {
  color: var(--primary);
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 0.75rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
}

.form-group {
  margin-bottom: 1rem;
  flex: 1;
}

.label-with-help {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  margin-bottom: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #f8f9fa;
  font-size: 15px;
}

.required-icon {
  color: #dc3545;
  font-size: 0.85rem;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-left: 2px;
  margin-bottom: 8px;
}

.limited-width-input {
  width: 100%;
}

.user-select-container {
  margin-bottom: 0.5rem;
}

.custom-autocomplete-wrapper {
  position: relative;
  width: 100%;
  max-width: 300px;

  .form-control {
    width: 100%;
    padding: 6px 10px;
    padding-right: 30px;
    font-size: 0.8125rem;
    line-height: 1.5;
    height: 32px;
    color: #fff;
    background-color: #212529;
    border: 1px solid #495057;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out;

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }

  .select-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    pointer-events: none;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 8px;
      height: 8px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 9999;
    margin-top: 4px;
    padding: 0.5rem 0;
    background-color: #212529;
    border: 1px solid #495057;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;

    &.show {
      display: block;
    }
  }

  .dropdown-item {
    padding: 0.375rem 0.75rem;
    color: #fff;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      background-color: #343a40;
    }

    &.active {
      background-color: var(--primary);
    }

    &.selected {
      background-color: #343a40;
    }

    i {
      margin-left: 8px;
      font-size: 0.875rem;
      color: var(--primary);
    }
  }
}

.selected-users-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4.2px 7.8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }

  i {
    margin-right: 5px;
    font-size: 12px;
  }
}

.form-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 15px;
  color: #f8f9fa;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

/* Estilos para upload de CSV */
.csv-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 1px dashed #495057;
  border-radius: 8px;
  background: linear-gradient(to right, #151719, #2c3034);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
  min-height: 180px;

  &:hover {
    background: linear-gradient(to right, #212529, #343a40);
    border-color: var(--primary);
  }

  &.drag-over {
    border-color: var(--primary);
    background: linear-gradient(to right, #212529, #343a40);
  }

  .upload-icon {
    width: 50px;
    height: 50px;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid #adb5bd;
      border-radius: 50%;
    }

    i {
      font-size: 1.25rem;
      color: #adb5bd;
    }
  }

  .upload-text {
    color: #f8f9fa;
    margin-bottom: 0.75rem;
    text-align: center;
    font-size: 15px;
  }

  .file-icon {
    margin-bottom: 1rem;

    i {
      font-size: 2.5rem;
      color: #adb5bd;
    }
  }

  .file-name {
    color: #e9ecef;
    font-size: 0.9375rem;
    margin-bottom: 0.25rem;
    font-weight: 500;
  }

  .file-size {
    color: #adb5bd;
    font-size: 0.8125rem;
    margin-bottom: 1rem;
  }

  .file-replace-text {
    color: #adb5bd;
    font-size: 0.75rem;
    margin-bottom: 0;
    font-style: italic;
  }

  .btn-browse {
    background-color: #343a40;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    cursor: pointer;
    font-size: 0.8125rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: #495057;
    }
  }
}

/* Removido o estilo .selected-file pois agora o arquivo é mostrado diretamente na área de upload */

.csv-users-preview {
  margin-bottom: 1rem;

  .preview-header {
    margin-bottom: 0.5rem;
    color: #adb5bd;
    font-size: 0.8125rem;
  }

  .more-users {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: rgba(73, 80, 87, 0.3);
    border-radius: 4px;
    color: #adb5bd;
    font-size: 0.7rem;
  }
}

.csv-info {
  margin-top: 1rem;

  .csv-format-text {
    margin: 0 0 0.5rem 0;
    color: #f8f9fa;
    font-size: 15px;
  }

  .csv-example {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .example-label {
      color: #f8f9fa;
      font-size: 15px;
    }

    .example-csv {
      color: var(--primary);
      text-decoration: none;
      font-size: 15px;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .csv-options-row {
    display: flex;
    gap: 1rem;

    .csv-option {
      label {
        display: block;
        margin-bottom: 0.5rem;
        color: #f8f9fa;
        font-size: 15px;
      }
    }
  }
}
</style>
