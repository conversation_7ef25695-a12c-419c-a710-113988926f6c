<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use local_offermanager\enrol_setup;

use local_offermanager\constants;
use local_offermanager\persistent\offer_user_enrol_model;


/**
 * Trait course_enrolment_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait course_enrolment_trait
{
    /**
     * Retorna os ids das instâncias de inscrição que estão associadas a instância.
     *
     * @return array
     */
    public function get_enrol_ids()
    {
        $offer_classes = $this->get_classes();

        return array_map(
            function ($offer_class) {
                return $offer_class->get('enrolid');
            },
            $offer_classes
        );
    }

    /**
     * Retorna as instâncias de inscrição que estão associadas a este registro.
     *
     * @return array
     */
    public function get_enrol_instances()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return [];
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->get_records_select(
            'enrol',
            "id {$insql}",
            $inparams
        );
    }

    public function get_classes_with_disabled_enrol_instances(): array
    {
        $disabled_classes = [];
        $classes = $this->get_classes();

        if (!$classes) {
            return $disabled_classes;
        }

        foreach ($classes as $class) {
            if ($class->get_mapped_field('status')) {
                $disabled_classes[] = $class;
            }
        }

        return $disabled_classes;
    }

    /**
     * Verifica se a instância possui instâncias de inscrição criadas.
     *
     * @return bool
     */
    public function has_enrol_instances()
    {
        return count($this->get_enrol_ids()) > 0;
    }

    /**
     * Retorna as inscrições associadas a instância.
     *
     * @return array
     */
    public function get_user_enrolments()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->get_records_select(
            'user_enrolments',
            "enrolid {$insql}",
            $inparams
        );
    }

    /**
     * Verifica se existem inscrições associadas a instância.
     *
     * @return array
     */
    public function has_user_enrolments()
    {
        global $DB;

        $enrolids = $this->get_enrol_ids();

        if (!$enrolids) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($enrolids);

        return $DB->record_exists_select(
            'user_enrolments',
            "enrolid {$insql}",
            $inparams
        );
    }

    /**
     * Verifica se o usuário possui uma inscrição ativa no curso atual
     * considerando apenas os métodos offer_manual, offer_self e offer_automatic
     * 
     * @param int $userid ID do usuário a ser verificado
     * @param int|null $excluded_instance_id Enrol instance id para desconsiderar na validação
     * @return bool True se o usuário tem inscrição ativa, False caso contrário
     */
    public function has_active_user_enrolment(int $userid, int|null $excluded_instance_id = null)
    {
        global $DB;

        $course = $this->get_course();
        $now = time();

        if (empty($course) || empty($userid)) {
            return false;
        }

        $params = [];

        $params[] = $course->id;
        $params[] = $userid;
        $params[] = ENROL_USER_ACTIVE;
        $params[] = ENROL_INSTANCE_ENABLED;
        $params[] = $now;
        $params[] = $now;

        $offer_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!$offer_plugins) {
            return false;
        }

        $and = '';

        if ($excluded_instance_id) {
            $and .= 'AND ue.enrolid <> ?';
            $params[] = $excluded_instance_id;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($offer_plugins);

        $sql = "SELECT ue.id
            FROM {user_enrolments} ue
                JOIN {enrol} e ON e.id = ue.enrolid
            WHERE e.courseid = ?
                AND ue.userid = ?
                AND ue.status = ?
                AND e.status = ?
                AND (ue.timeend = 0 OR ue.timeend > ?)
                {$and}
                AND e.enrol {$insql}
        ";
        return $DB->record_exists_sql($sql, array_merge($params, $inparams));
    }

    /**
     * Verifica se o usuário possui uma inscrição ativa no curso atual
     * considerando apenas os métodos offer_manual, offer_self e offer_automatic
     * 
     * @param int $userid ID do usuário a ser verificado
     * @param int|null $excluded_instance_id Enrol instance id para desconsiderar na validação
     * @return bool True se o usuário tem inscrição ativa, False caso contrário
     */
    static public function get_active_user_enrolment(int $courseid, int $userid)
    {
        global $DB;

        $now = time();

        if (empty($courseid) || empty($userid)) {
            return false;
        }

        $params = [];

        $params[] = $courseid;
        $params[] = $userid;
        $params[] = ENROL_USER_ACTIVE;
        $params[] = ENROL_INSTANCE_ENABLED;
        $params[] = $now;
        $params[] = $now;

        $offer_plugins = enrol_setup::get_dependent_enrol_plugins();

        if (!$offer_plugins) {
            return false;
        }

        [$insql, $inparams] = $DB->get_in_or_equal($offer_plugins);

        $sql = "SELECT ue.id
            FROM {user_enrolments} ue
                JOIN {enrol} e ON e.id = ue.enrolid
            WHERE e.courseid = ?
                AND ue.userid = ?
                AND ue.status = ?
                AND e.status = ?
                AND (ue.timeend = 0 OR ue.timeend > ?)
                AND e.enrol {$insql}
        ";
        return $DB->get_record_sql($sql, array_merge($params, $inparams));
    }

    /**
     * Retorna as inscrições do usuário no curso que ainda não iniciaram.
     *
     * @param int $userid ID do usuário.
     * @return array Array de objetos offer_user_enrol_model.
     */
    public function get_user_enrolments_to_start(int $userid): array
    {
        if (empty($userid)) {
            return [];
        }

        $enrolments = offer_user_enrol_model::get_offer_enrolments_by_user_and_course(
            $userid,
            $this->get('courseid'),
            constants::OFFER_USER_ENROL_SITUATION_ENROLED
        );
        if (empty($enrolments)) {
            return [];
        }

        $now = time();
        $enrolments_to_start = [];

        foreach ($enrolments as $enrolment) {
            $timestart = $enrolment->get_field_from_user_enrolment('timestart');
            if (!empty($timestart) && $timestart > $now) {
                $enrolments_to_start[] = $enrolment;
            }
        }

        return $enrolments_to_start;
    }

    /**
     * Cancela as inscrições do usuário no curso que ainda não iniciaram.
     *
     * @param int $userid ID do usuário cujas inscrições serão canceladas.
     * @param int $offerclassid ID da nova turma na qual o usuário foi inscrito (para a mensagem de motivo).
     * @return bool True se alguma inscrição foi cancelada, false caso contrário.
     */
    public function cancel_user_enrolments_to_start(int $userid, int $offerclassid): bool
    {
        global $USER;

        $enrolments_to_cancel = $this->get_user_enrolments_to_start($userid);

        if (empty($enrolments_to_cancel)) {
            return false;
        }

        $canceled_count = 0;
        foreach ($enrolments_to_cancel as $enrolment) {
            $reason = get_string(
                'message:reason_cancel_previous_enrolment',
                'local_offermanager',
                (object) [
                    'adminid' => $USER->id,
                    'userid' => $userid,
                    'newofferclassid' => $offerclassid
                ]
            );

            if ($enrolment->set_canceled($reason)) {
                $canceled_count++;
            }
        }

        return $canceled_count > 0;
    }

    public function enable_enrol_instances()
    {
        $classes = $this->get_classes_with_disabled_enrol_instances();

        if (!$classes) {
            return;
        }

        foreach ($classes as $class) {
            $class->enable_enrol_instance();
        }
    }

    /**
     * Verifica se o usuário está inscrito no curso e se sua inscrição está ativa.
     *
     * @param int $userid ID do usuário.
     * @return bool Verdadeiro se o usuário estiver inscrito ativamente.
     */
    public function is_user_enrolled_and_active(int $userid): bool
    {
        global $DB;

        $courseid = $this->get('courseid');
        $now = time();

        $sql = "
            SELECT ue.id
            FROM {user_enrolments} ue
                JOIN {enrol} e ON e.id = ue.enrolid
            WHERE ue.userid = :userid
                AND e.courseid = :courseid
                AND ue.status = :status
                AND e.status = :enrolstatus
                AND (ue.timestart = 0 OR ue.timestart <= :now1)
                AND (ue.timeend = 0 OR ue.timeend >= :now2)
        ";

        $params = [
            'userid' => $userid,
            'courseid' => $courseid,
            'status' => ENROL_USER_ACTIVE,
            'enrolstatus' => ENROL_INSTANCE_ENABLED,
            'now1' => $now,
            'now2' => $now,
        ];

        return $DB->record_exists_sql($sql, $params);
    }

    /**
     * Busca usuários potenciais para matrícula no curso.
     *
     * @param string $search_string The search string to filter potential users.
     * @param array $excluded_userids Array of user IDs to exclude from the search.
     * @param bool $limited Whether to limit the search results to 100.
     * @return array Array of user objects.
     */
    public function get_potential_users_to_enrol(string $search_string = '', array $excluded_userids = [], bool $limited = true)
    {
        global $DB;

        $courseid = $this->get('courseid');
        $now = time();

        $excluded_userids = array_map(
            function ($userid) {
                return (int) $userid;
            },
            $excluded_userids
        );

        $params = [
            $courseid,
            ENROL_USER_ACTIVE,
            $now,
            $now,
            ENROL_INSTANCE_ENABLED,
        ];

        $and = $enrol_and = '';

        $offer_plugins = enrol_setup::get_dependent_enrol_plugins();

        if ($offer_plugins) {
            [$enrol_insql, $enrol_inparams] = $DB->get_in_or_equal($offer_plugins);
            $enrol_and .= " AND e.enrol {$enrol_insql}";

            $params = array_merge($params, $enrol_inparams);
        }

        if ($search_string) {
            $and .= " AND " . $DB->sql_like('CONCAT(u.firstname, \' \', u.lastname)', '?', false, false);
            $params[] = "%{$search_string}%";
        }

        $siteadmins = get_config('moodle', 'siteadmins');

        if ($siteadmins) {

            $siteadmins = explode(',', $siteadmins);

            $siteadmins = array_map(
                function ($userid) {
                    return (int) $userid;
                },
                $siteadmins
            );

            $excluded_userids = array_merge($excluded_userids, $siteadmins);
        }

        $selected_role_userids = $DB->get_fieldset_sql(
            "SELECT DISTINCT mra.userid
            FROM {role_assignments} mra
                JOIN {context} mc ON (mc.id = mra.contextid)
                JOIN {role} mr ON (mr.id = mra.roleid)
            WHERE mr.shortname IN ('client', 'joomdlews', 'admin_sebrae')
                AND mc.contextlevel = 10
            "
        );

        if ($selected_role_userids) {

            $selected_role_userids = array_map(
                function ($userid) {
                    return (int) $userid;
                },
                $selected_role_userids
            );

            $excluded_userids = array_merge($excluded_userids, $selected_role_userids);
        }

        if ($excluded_userids) {

            $excluded_userids = array_unique($excluded_userids);

            sort($excluded_userids);

            [$insql, $inparams] = $DB->get_in_or_equal(
                $excluded_userids,
                SQL_PARAMS_QM,
                '',
                false
            );

            $and .= " AND u.id {$insql}";
    
            $params = array_merge($params, $inparams);
        }

        $limit = $limited ? 'LIMIT 0, 200' : '';

        $sql = "SELECT u.*
        FROM {user} u
        WHERE u.id > 1
            AND u.deleted = 0
            AND u.suspended = 0
            AND u.username NOT IN ('lf.api.adminuser', 'lf.api.clientuser')
            AND NOT EXISTS (
                SELECT 1
                FROM {user_enrolments} ue
                    JOIN {enrol} e ON (e.id = ue.enrolid)
                WHERE ue.userid = u.id
                    AND e.courseid = ?
                    AND ue.status = ?
                    AND (ue.timestart = 0 OR ue.timestart <= ?)
                    AND (ue.timeend = 0 OR ue.timeend >= ?)
                    AND e.status = ?
                    {$enrol_and}
            )
            {$and}
        {$limit}
        ";

        $users = $DB->get_records_sql($sql, $params);

        return $users;
    }

    /**
     * Retorna a última matrícula do usuário no curso atual.
     *
     * @param int $userid ID do usuário
     * @return stdClass|null Objeto com os dados da matrícula ou null se não encontrado
     */
    public function get_last_user_enrolments($userid)
    {
        global $DB;

        return $DB->get_record_sql(
            "SELECT ue.*
            FROM {user_enrolments} ue
                JOIN {enrol} e ON (e.id = ue.enrolid)
            WHERE e.courseid = :courseid 
                AND ue.userid = :userid
            ORDER BY ue.timemodified DESC
            LIMIT 0, 1
            ",
            [
                'courseid' => $this->get('courseid'),
                'userid' => $userid
            ]
        );
    }
}
