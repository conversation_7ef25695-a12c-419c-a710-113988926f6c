<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON>le is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\trait;

use local_offermanager\persistent\offer_user_enrol_model;
use core\event\enrol_instance_created;
use \core\event\enrol_instance_updated;
use local_offermanager\persistent\offer_class_model;
use local_offermanager\constants;
use course_enrolment_manager;
use core\session\manager;
use stdClass;
use progress_trace;
use moodle_exception;
use context_course;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\enrol_setup;
use context_offer_class;
use local_offermanager\form\self_reenrol_form;
use local_recertification\task\recertificate;
use moodle_url;
use cache;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait offer_enrol_plugin_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait offer_enrol_plugin_trait
{
    private static array $OPTIONAL_FIELDS = [
        'enableenddate',
        'enddate',
        'enablepreenrolment',
        'preenrolmentstartdate',
        'preenrolmentenddate',
        'description',
        'enableenrolperiod',
        'enrolperiod',
        'minusers',
        'maxusers',
        'roleid',
        'enablereenrol',
        'reenrolmentsituations',
        'enableextension',
        'extensionperiod',
        'extensiondaysavailable',
        'extensionmaxrequests',
        'extensionallowedsituations'
    ];
    private static array $INT_BOOL_FIELDS = [
        'enableenddate',
        'enablepreenrolment',
        'enableenrolperiod',
        'enablereenrol',
        'enableextension'
    ];
    private static array $TEXT_FIELDS = [
        'description'
    ];
    private static array $LIST_FIELDS = [
        'teachers',
        'reenrolmentsituations'
    ];
    private static array $JSON_FIELDS = [
        'extensiondata'
    ];

    /**
     * Retorna o nome do plugin de inscrição.
     *
     * @return string Nome do plugin de inscrição.
     */
    public function get_name(): string
    {
        $classname = self::class;
        $classname = str_replace('enrol_', '', $classname);
        $classname = str_replace('_plugin', '', $classname);
        return $classname;
    }

    /**
     * Enrol user into course via enrol instance.
     *
     * @param stdClass $instance
     * @param int $userid
     * @param int $roleid optional role id
     * @param int $timestart 0 means unknown
     * @param int $timeend 0 means forever
     * @param int $status default to ENROL_USER_ACTIVE for new enrolments, no change by default in updates
     * @param bool $recovergrades restore grade history
     * @return void
     */
    public function enrol_user(stdClass $instance, $userid, $roleid = null, $timestart = 0, $timeend = 0, $status = null, $recovergrades = null)
    {
        global $DB;

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (!$offerclass) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        $offercourse = $offerclass->get_offer_course();

        if ($offercourse->has_active_user_enrolment($userid)) {
            $user = $DB->get_record('user', ['id' => $userid]);

            $user_fullname = fullname($user);
            throw new moodle_exception('error:user_has_active_enrolment', 'local_offermanager', '', $user_fullname);
        }

        $courseid = $offercourse->get('courseid');

        if ($offercourse->user_has_activity($userid, $courseid)) {
            $recertificate_task = new recertificate;
            $last_enrolment = $offercourse->get_last_user_enrolments($userid);
            $ueid = $last_enrolment ? $last_enrolment->id : null;
            $error = $recertificate_task->reset_user_data($userid, $courseid, $ueid);

            if ($error) {
                // Precisamos fazer algo? talvez não.
            }

            \cache::make('core', 'completion')->purge();
            \cache::make('core', 'coursecompletion')->purge();
            \cache::make('core', 'coursemodinfo')->purge();
            \cache::make('core', 'course_user_dates')->purge();
            \cache::make('core', 'user_course_content_items')->purge();

            manager::kill_user_sessions($userid);
        }

        $instance = $offerclass->get_enrol_instance();

        list($timestart, $timeend) = $offerclass->get_enrol_period($timestart, $timeend);

        $roleid = $roleid ?: ($offerclass->get_mapped_field('roleid') ?: 5);

        // Talvez o usuário tenha inscrição para iniciar neste curso e essa inscrição é uma substituição
        // então vamos cancelar essas inscrições que não iniciaram
        $offercourse->cancel_user_enrolments_to_start($userid, $offerclass->get('id'));

        parent::enrol_user($instance, $userid, $roleid, $timestart, $timeend, $status, $recovergrades);

        $ue = $DB->get_record(
            'user_enrolments',
            [
                'userid' => $userid,
                'enrolid' => $instance->id
            ]
        );


        $offer_ue = new offer_user_enrol_model(
            0,
            (object)[
                'offerclassid' => $offerclass->get('id'),
                'ueid' => $ue->id,
                'userid' => $userid,
                'courseid' => $offercourse->get('courseid')
            ]
        );

        $offer_ue->save();
    }

    // Métodos de interface do método de inscrição dentro do curso

    /**
     * Return whether or not, given the current state, it is possible to edit an instance
     * of this enrolment plugin in the course. Used by the standard editing UI
     * to generate a link to the edit instance form if editing is allowed.
     *
     * @param stdClass $instance
     * @return boolean
     */
    public function can_add_instance($courseid)
    {
        return false;
    }

    /**
     * Is it possible to hide/show enrol instance via standard UI?
     *
     * @param stdClass $instance
     * @return bool
     */
    public function can_hide_show_instance($instance)
    {
        return false;
    }

    /**
     * Return whether or not, given the current state, it is possible to edit an instance
     * of this enrolment plugin in the course. Used by the standard editing UI
     * to generate a link to the edit instance form if editing is allowed.
     *
     * @param stdClass $instance
     * @return boolean
     */
    public function can_edit_instance($instance)
    {
        return false;
    }

    /**
     * Is it possible to delete enrol instance via standard UI?
     *
     * @param stdClass $instance
     * @return bool
     */
    public function can_delete_instance($instance)
    {
        return false;
    }

    /**
     * Displays the enrolment type in the "Add method" selector
     *
     * @return boolean
     */
    public function use_standard_editing_ui()
    {
        return false;
    }

    /**
     * Adiciona uma nova instância de inscrição manual.
     *
     * @param object $course Objeto do curso.
     * @param array $fields Campos do formulário.
     * @return int ID da instância criada.
     */
    public function add_instance($course, array|null $fields = NULL)
    {
        global $DB;

        if (empty($fields['offercourseid'])) {
            throw new moodle_exception('error:offercourseid_required', 'local_offermanager');
        }

        $offercourse = offer_course_model::get_record(
            [
                'id' => $fields['offercourseid']
            ],
            MUST_EXIST
        );

        if (empty($fields['classname'])) {
            throw new moodle_exception('error:classname_required', 'local_offermanager');
        }
        if (empty($fields['startdate'])) {
            throw new moodle_exception('error:startdate_required', 'local_offermanager');
        }

        $instance = new stdClass();
        $instance->courseid = $course->id;
        $instance->enrol = $this->get_name();
        $instance->status = $offercourse->is_active() ? ENROL_INSTANCE_ENABLED : ENROL_INSTANCE_DISABLED;
        $instance->timecreated = time();
        $instance->timemodified = time();
        $instance->sortorder = $DB->get_field('enrol', 'COALESCE(MAX(sortorder), -1) + 1', array('courseid' => $course->id));

        $extensiondata = new \stdClass();
        $extensiondata->period = isset($fields['extensionperiod']) ? $fields['extensionperiod'] : 0;
        $extensiondata->days_available = isset($fields['extensiondaysavailable']) ? $fields['extensiondaysavailable'] : 0;
        $extensiondata->max_requests = isset($fields['extensionmaxrequests']) ? $fields['extensionmaxrequests'] : 0;

        $extensiondata->allowed_situations = isset($fields['extensionallowedsituations']) && is_array($fields['extensionallowedsituations'])
            ? $fields['extensionallowedsituations']
            : []
        ;

        $fields['extensiondata'] = $extensiondata;

        $this->apply_fields_data($instance, $fields);
        $instance->id = $DB->insert_record('enrol', $instance);

        $offercourse->add_class($instance->id);

        $event = enrol_instance_created::create_from_record($instance);
        $event->trigger();

        return $instance->id;
    }

    /**
     * Atualiza uma instância de inscrição manual existente.
     *
     * @param stdClass $instance Instância de inscrição existente.
     * @param stdClass $data Dados a serem atualizados.
     * @return bool True se a atualização foi bem-sucedida, false caso contrário.
     */
    public function update_instance($instance, $data)
    {
        global $DB;

        $extensiondata = new \stdClass();
        $extensiondata->period = isset($data['extensionperiod']) ? $data['extensionperiod'] : 0;
        $extensiondata->days_available = isset($data['extensiondaysavailable']) ? $data['extensiondaysavailable'] : 0;
        $extensiondata->max_requests = isset($data['extensionmaxrequests']) ? $data['extensionmaxrequests'] : 0;
        $extensiondata->allowed_situations = $data['extensionallowedsituations'] ?? [];

        $data['extensiondata'] = $extensiondata;

        $this->apply_fields_data($instance, (array) $data);

        $instance->timemodified = time();

        $result = $DB->update_record('enrol', $instance);

        if ($result) {
            $event = enrol_instance_updated::create_from_record($instance);
            $event->trigger();
        }

        return $result;
    }

    /**
     * Perform custom validation of the data used to edit the instance.
     *
     * @param array $data array of ("fieldname"=>value) of submitted data
     * @param array $files array of uploaded files "element_name"=>tmp_file_path
     * @param object $instance The instance data loaded from the DB.
     * @param context $context The context of the instance we are editing
     * @return array of "element_name"=>"error_description" if there are errors,
     *         or an empty array if everything is OK.
     */
    public function edit_instance_validation($data, $files, $instance, $context): array
    {
        $errors = [];

        $this->validate_required_fields($data, $errors);
        $this->validate_date_fields($data, $errors);
        $this->validate_user_limits($data, $errors);
        $this->validate_numeric_fields($data, $errors);
        $this->validate_extension_fields($data, $errors);

        return $errors;
    }

    // Métodos relacionados a self enrol

    /**
     * Does this plugin support some way to user to self enrol?
     *
     * @param stdClass $instance course enrol instance
     *
     * @return bool - true means show "Enrol me in this course" link in course UI
     */
    public function show_enrolme_link(stdClass $instance)
    {
        return false;
    }

    /**
     * Does this plugin support some way to self enrol?
     * This function doesn't check user capabilities. Use can_self_enrol to check capabilities.
     *
     * @param stdClass $instance enrolment instance
     * @return bool - true means "Enrol me in this course" link could be available.
     */
    public function is_self_enrol_available(stdClass $instance)
    {
        return false;
    }

    /**
     * Checks if user can self enrol.
     *
     * @param stdClass $instance enrolment instance
     * @param bool $checkuserenrolment if true will check if user enrolment is inactive.
     *             used by navigation to improve performance.
     * @return bool|string true if successful, else error message or false
     */
    public function can_self_enrol(stdClass $instance, $checkuserenrolment = true)
    {
        return false;
    }

    /**
     * Returns list of unenrol links for all enrol instances in course.
     *
     * @param stdClass $instance
     * @return moodle_url or NULL if self unenrolment not supported
     */
    public function get_unenrolself_link($instance)
    {
        global $USER, $OUTPUT;

        $name = $this->get_name();

        if ($instance->enrol !== $name) {
            throw new moodle_exception('invalid enrol instance!');
        }

        if ($instance->courseid == SITEID) {
            return NULL;
        }

        if (!enrol_is_enabled($name)) {
            return NULL;
        }

        if ($instance->status != ENROL_INSTANCE_ENABLED) {
            return NULL;
        }

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (!$offerclass) {
            return null;
        }

        $context = context_offer_class::instance($offerclass->get('id'), MUST_EXIST);

        if (!has_capability("enrol/$name:unenrolself", $context)) {
            return NULL;
        }

        $offeruserenrol = $offerclass->get_user_offer_user_enrolment($USER->id);

        if (
            !$offeruserenrol
            || !$offerclass->is_user_enrolled_and_active($USER->id)
            || !$offeruserenrol->is_active()
        ) {
            return NULL;
        }

        $offeruserenrolid = $offeruserenrol->get('id');
        echo $OUTPUT->render_from_template('local_offermanager/self_cancel_modal', array('offeruserenrol' => $offeruserenrolid));
        return new moodle_url("/local/offermanager/unenrolself.php", ['id' => $offeruserenrolid]);
    }

    // Métodos de funcionalidade

    /**
     * Forces synchronisation of user enrolments.
     *
     * This is important especially for external enrol plugins,
     * this function is called for all enabled enrol plugins
     * right after every user login.
     *
     * @param object $user user record
     * @return void
     */
    public function sync_user_enrolments($user)
    {
        // override if necessary
    }

    /**
     * Returns edit icons for the page with list of instances
     * @param stdClass $instance
     * @return array
     */
    public function get_action_icons(stdClass $instance)
    {
        return [];
    }

    // Validações

    /**
     * Validates required fields in the submission data
     * 
     * @param array $data The submitted data to validate
     * @param array &$errors Reference to array where errors will be stored
     * @return void
     */
    private function validate_required_fields(array $data, array &$errors): void
    {
        $required_fields = [
            'offercourseid' => 'error:offercourseid_required',
            'classname' => 'error:classname_required',
            'startdate' => 'error:startdate_required'
        ];

        foreach ($required_fields as $field => $error_key) {
            if (empty($data[$field])) {
                $errors[$field] = get_string($error_key, 'local_offermanager');
            }
        }
    }

    /**
     * Validates date fields and their relationships
     * 
     * @param array $data The submitted data to validate
     * @param array &$errors Reference to array where errors will be stored
     * @return void
     */
    private function validate_date_fields(array $data, array &$errors): void
    {
        if (!empty($data['enableenddate'])) {
            if (empty($data['enddate'])) {
                $errors['enddate'] = get_string('error:enddate_required', 'local_offermanager');
            } elseif (strtotime($data['enddate']) <= strtotime($data['startdate'])) {
                $errors['enddate'] = get_string('error:enddate_before_startdate', 'local_offermanager');
            }
        }

        if (!empty($data['enablepreregistration'])) {
            if (empty($data['preregistrationstartdate'])) {
                $errors['preregistrationstartdate'] = get_string('error:preregistrationstartdate_required', 'local_offermanager');
            } elseif (empty($data['preregistrationenddate'])) {
                $errors['preregistrationenddate'] = get_string('error:preregistrationenddate_required', 'local_offermanager');
            } elseif (strtotime($data['preregistrationenddate']) <= strtotime($data['preregistrationstartdate'])) {
                $errors['preregistrationenddate'] = get_string('error:preregistrationenddate_before_preregistrationstartdate', 'local_offermanager');
            }
        }
    }

    /**
     * Validates user limit fields (min/max users)
     * 
     * @param array $data The submitted data to validate
     * @param array &$errors Reference to array where errors will be stored
     * @return void
     */
    private function validate_user_limits(array $data, array &$errors): void
    {
        if (!empty($data['minusers'])) {
            if (!is_numeric($data['minusers'])) {
                $errors['minusers'] = get_string('error:minusers_numeric', 'local_offermanager');
            } elseif (!empty($data['maxusers']) && $data['maxusers'] < $data['minusers']) {
                $errors['maxusers'] = get_string('error:maxusers_less_than_minusers', 'local_offermanager');
            }
        }
    }

    /**
     * Validates numeric fields
     * 
     * @param array $data The submitted data to validate
     * @param array &$errors Reference to array where errors will be stored
     * @return void
     */
    private function validate_numeric_fields(array $data, array &$errors): void
    {
        $numeric_fields = [
            'maxusers' => 'error:maxusers_numeric',
            'enrolperiod' => 'error:enrolperiod_numeric'
        ];

        foreach ($numeric_fields as $field => $error_key) {
            if (!empty($data[$field]) && !is_numeric($data[$field])) {
                $errors[$field] = get_string($error_key, 'local_offermanager');
            }
        }
    }

    /**
     * Validates extension related fields
     * 
     * @param array $data The submitted data to validate
     * @param array &$errors Reference to array where errors will be stored
     * @return void
     */
    private function validate_extension_fields(array $data, array &$errors): void
    {
        if (!empty($data['enableextension'])) {
            $required_extension_fields = [
                'extensiondaysavailable' => 'error:extensiondaysavailabie_required',
                'extensionmaxrequests' => 'error:extensionmaxrequests_required'
            ];

            foreach ($required_extension_fields as $field => $error_key) {
                if (empty($data[$field])) {
                    $errors[$field] = get_string($error_key, 'local_offermanager');
                }
            }
        } elseif (!empty($data['extensionmaxrequests']) && !is_numeric($data['extensionmaxrequests'])) {
            $errors['extensionmaxrequests'] = get_string('error:extensionmaxrequests_numeric', 'local_offermanager');
        }
    }

    // Métodos auxiliares

    /**
     * Aplica os dados dos campos à instância.
     *
     * @param stdClass $instance Instância a ser modificada.
     * @param array $fields Campos a serem aplicados.
     * @return void
     */
    public static function apply_fields_data(stdClass &$instance, array $fields)
    {
        foreach (enrol_setup::FIELDS_MAP as $data_field => $instance_field) {
            if (array_key_exists($data_field, $fields)) {
                $new_value = $fields[$data_field];

                if (in_array($data_field, self::$INT_BOOL_FIELDS)) {
                    $new_value = !empty($new_value) ? 1 : 0;
                } else if (in_array($data_field, self::$TEXT_FIELDS)) {
                    $new_value = !empty($new_value) ? $new_value : '';
                } else if (in_array($data_field, self::$LIST_FIELDS)) {
                    $new_value = (is_array($new_value) && count($new_value) > 0) ? implode(', ', $new_value) : '';
                } else if (in_array($data_field, self::$JSON_FIELDS)) {
                    $new_value = ($new_value instanceof \stdClass)
                        ? json_encode($new_value)
                        : enrol_setup::get_default_extension_data();
                } else if (in_array($data_field, self::$OPTIONAL_FIELDS)) {
                    $new_value =  $new_value;
                }

                $instance->{$instance_field} = $new_value;
            }
        }
    }

    /**
     * Delete course enrol plugin instance, unenrol all users.
     * @param object $instance
     * @return void
     */
    public function delete_instance($instance)
    {
        $offer_class = offer_class_model::get_by_enrolid($instance->id);

        if (!$offer_class) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        $offer_class->delete();

        parent::delete_instance($instance);
    }

    /**
     * Execute synchronisation.
     * @param progress_trace $trace
     * @return int exit code, 0 means ok
     */
    public function sync(progress_trace $trace)
    {
        $this->process_expirations($trace);
        return 0;
    }

    /**
     * Do any enrolments need expiration processing.
     *
     * Plugins that want to call this functionality must implement 'expiredaction' config setting.
     *
     * @param progress_trace $trace
     * @param int $courseid one course, empty mean all
     * @return bool true if any data processed, false if not
     */
    public function process_expirations(progress_trace $trace, $courseid = null)
    {
        $name = $this->get_name();

        if (!enrol_is_enabled($name)) {
            $trace->finished();
            return false;
        }

        $offerclasses = $this->get_active_classes();

        if (!$offerclasses) {
            $trace->output("No classes to process in of enrol_$name plugin...");
        }

        $processed = false;

        foreach ($offerclasses as $offerclass) {
            if (!$processed) {
                $trace->output("Starting processing of enrol_$name expirations...");
                $processed = true;
            }
            $offerclass->process_unsuccessful_enrolments();
        }

        if ($processed) {
            $trace->output("...finished processing of enrol_$name expirations");
        } else {
            $trace->output("No expired enrol_$name enrolments detected");
        }
        $trace->finished();

        return $processed;
    }

    public function get_active_classes()
    {
        global $DB;

        return offer_class_model::get_records_select(
            'enrol = :name AND status = :status',
            [
                'name' => $this->get_name(),
                'status' => constants::OFFER_STATUS_ACTIVE
            ]
        );
    }

    /**
     * Attempt to automatically gain temporary guest access to course,
     * calling code has to make sure the plugin and instance are active.
     *
     * This should return either a timestamp in the future or false.
     *
     * @param stdClass $instance course enrol instance
     * @return bool|int false means no guest access, integer means timeend
     */
    public function try_guestaccess(stdClass $instance)
    {
        global $USER;

        if (isguestuser() || $instance->status != ENROL_INSTANCE_ENABLED) {
            return false;
        }

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (!$offerclass) {
            return false;
        }

        $offer_user_enrol = $offerclass->get_user_offer_user_enrolment($USER->id);

        if (!$offer_user_enrol) {
            return false;
        }

        if ($offer_user_enrol->is_approved() || $offer_user_enrol->is_completed()) {
            $offer_course = $offerclass->get_offer_course();
            $course_enddate = $offer_course->get_course_field('enddate');
            $enddate = $course_enddate ? (int) $course_enddate : 9999999999;

            return $enddate;
        }

        return false;
    }

    /**
     * Checks if reenrolment is available for this enrolment instance.
     *
     * @return bool True if reenrolment is available, false otherwise.
     */
    public function reenrol_available()
    {
        return false;
    }

    /**
     * Creates course enrol form, checks if form submitted
     * and enrols user if necessary. It can also redirect.
     *
     * @param stdClass $instance
     * @return string html text, usually a form in a text box
     */
    public function enrol_page_hook(stdClass $instance)
    {
        global $OUTPUT, $USER;

        $context = context_course::instance($instance->courseid);

        if (!has_capability('enrol/offer_self:enrolself', $context)) {
            return false;
        }

        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (
            !$offerclass
            || !$offerclass->is_active()
            || $offerclass->is_finished()
        ) {
            return false;
        }

        $offeruserenrol = $offerclass->get_user_offer_user_enrolment($USER->id);

        if (!$offeruserenrol) {
            return false;
        }

        if ($offeruserenrol->can_reenrol()) {

            $form = new self_reenrol_form(null, $offeruserenrol->get('id'));
            $offeruserenrolid = optional_param('offeruserenrolid', 0, PARAM_INT);
            if ($offeruserenrol->get('id') == $offeruserenrolid) {
                if ($data = $form->get_data()) {
                    $reenrolled = $offeruserenrol->process_reenrolment();

                    if ($reenrolled) {
                        redirect(
                            new moodle_url('/course/view.php', ['id' => $instance->courseid]),
                            get_string('message:reenrolment_success', 'local_offermanager', $offerclass->get_name()),
                            null,
                            \core\output\notification::NOTIFY_SUCCESS
                        );
                    }
                }
            }

            $output = $form->render();
            echo $OUTPUT->box($output);
        }
    }

    /**
     * Unenrol user from course,
     * the last unenrolment removes all remaining roles.
     *
     * @param stdClass $instance
     * @param int $userid
     * @return void
     */
    public function unenrol_user(stdClass $instance, $userid)
    {
        $offerclass = offer_class_model::get_by_enrolid($instance->id);

        if (!$offerclass) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        $offer_user_enrol = $offerclass->get_user_offer_user_enrolment($userid);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        parent::unenrol_user($instance, $userid);

        $offer_user_enrol->delete();
    }

    /**
     * Does this plugin allow manual changes in user_enrolments table?
     *
     * All plugins allowing this must implement 'enrol/xxx:manage' capability
     *
     * @param stdClass $instance course enrol instance
     * @return bool - true means it is possible to change enrol period and status in user_enrolments table
     */
    public function allow_manage(stdClass $instance)
    {
        return true;
    }

    /**
     * Gets an array of the user enrolment actions
     *
     * @param course_enrolment_manager $manager
     * @param stdClass $ue
     * @return array An array of user_enrolment_actions
     */
    public function get_user_enrolment_actions(course_enrolment_manager $manager, $ue)
    {
        return [];
    }
}
