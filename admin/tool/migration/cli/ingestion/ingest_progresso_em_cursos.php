<?php

use tool_migration\models\progresso_curso;

define("CLI_SCRIPT", true);
require_once(__DIR__ . '/../../../../../config.php');

raise_memory_limit(MEMORY_HUGE);

$filepath = $CFG->dataroot . '/eadtech/migration/data/progresso-de-alunos-em-cursos.csv';
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read() as $row) {
    try {
        $counter += (int) progresso_curso::upsert_from_csv($row);
    } catch (\Throwable $th) {
        mtrace($th);
    }
}

print_r("\nATUALIZADOS: $counter");