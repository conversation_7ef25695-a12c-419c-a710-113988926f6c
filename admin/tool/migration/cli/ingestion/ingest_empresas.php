<?php

use tool_migration\models\empresa;

define("CLI_SCRIPT", true);
require_once(__DIR__ . '/../../../../../config.php');

raise_memory_limit(MEMORY_HUGE);

$filepath = $CFG->dataroot . '/eadtech/migration/data/empresas.csv';
$reader = new tool_migration\importers\readers\csv_reader($filepath);

$counter = 0;
foreach ($reader->read() as $row) {
    try {
        $counter += (int) empresa::upsert_from_csv($row);
    } catch (\Throwable $th) {
        mtrace($th);
    }
}

print_r("\nATUALIZADOS: $counter");