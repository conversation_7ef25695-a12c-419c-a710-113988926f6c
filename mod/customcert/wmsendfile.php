<?php
// This file is part of the customcert module for Mo<PERSON><PERSON> - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON><PERSON><PERSON>.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Compatibility file for downloading certificates
 * This file provides compatibility with the SimpleCertificate download pattern
 * by redirecting to the proper CustomCert download URL
 *
 * @package mod_customcert
 * @copyright 2025 CustomCert Team
 * @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(dirname(dirname(dirname(__FILE__))) . '/config.php');

// Require login
require_login();

$code = required_param('code', PARAM_TEXT); // Issue Code.

// Find the certificate issue by code
$issue = $DB->get_record('customcert_issues', ['code' => $code]);

// Se não encontrou no sistema ativo, buscar no histórico de recertificação
if (!$issue) {
    // Função para determinar qual tabela usar para CustomCert no histórico
    function get_customcert_history_table() {
        global $DB;

        if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cct'))) {
            return 'local_recertification_cct';
        }

        if ($DB->get_manager()->table_exists(new \xmldb_table('local_recertification_cc'))) {
            $columns = $DB->get_columns('local_recertification_cc');
            if (isset($columns['customcertid'])) {
                return 'local_recertification_cc';
            }
        }

        return null;
    }

    $history_table = get_customcert_history_table();
    if ($history_table) {
        $history_cert = $DB->get_record($history_table, ['code' => $code]);
        if ($history_cert) {
            // Verificar se o usuário tem permissão para baixar este certificado
            if ($history_cert->userid != $USER->id) {
                print_error('nopermissions', 'error', '', 'Baixar certificado de outro usuário');
            }

            // Construir o caminho do arquivo
            $filepath = "{$CFG->dataroot}/{$history_cert->fileurl}";

            if (file_exists($filepath)) {
                // Gerar nome do arquivo usando o nome do curso
                $coursename = isset($history_cert->coursename) ? $history_cert->coursename : 'Certificado';
                // Limpar caracteres especiais do nome do arquivo
                $coursename = preg_replace('/[^a-zA-Z0-9\s\-_]/', '', $coursename);
                $coursename = preg_replace('/\s+/', '_', trim($coursename));
                $filename = $coursename . '.pdf';

                // Enviar o arquivo
                header('Content-Description: File Transfer');
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Expires: 0');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');
                header('Content-Length: ' . filesize($filepath));

                ob_clean();
                flush();
                readfile($filepath);
                exit;
            } else {
                print_error('Arquivo de certificado não encontrado');
            }
        }
    }

    print_error('Certificate not found');
}

// Get the certificate
$customcert = $DB->get_record('customcert', ['id' => $issue->customcertid]);
if (!$customcert) {
    print_error('Certificate not found');
}

// Check permissions
$cm = get_coursemodule_from_instance('customcert', $customcert->id);
$context = context_module::instance($cm->id);

// Check if user can view this certificate
if ($issue->userid != $USER->id) {
    require_capability('mod/customcert:viewreport', $context);
} else {
    require_capability('mod/customcert:view', $context);
}

// Redirect to the proper download URL (para certificados ativos)
$url = new moodle_url('/mod/customcert/my_certificates.php', [
    'downloadcert' => 1,
    'userid' => $issue->userid,
    'certificateid' => $customcert->id
]);

redirect($url);
