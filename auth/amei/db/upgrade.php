<?php

function xmldb_auth_amei_upgrade($oldversion) {
    global $DB;
    $dbman = $DB->get_manager();

    if ($oldversion < 2025061200) {
        // Define table auth_amei_blacklist to be created.
        $table = new xmldb_table('auth_amei_blacklist');

        // Adding fields to table auth_amei_blacklist.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('cpf', XMLDB_TYPE_CHAR, '11', null, XMLDB_NOTNULL, null, null);
        $table->add_field('redirect_url', XMLDB_TYPE_CHAR, '255', null, null, null, null);

        // Adding keys to table auth_amei_blacklist.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);

        // Adding indexes to table auth_amei_blacklist.
        $table->add_index('cpf_idx', XMLDB_INDEX_NOTUNIQUE, ['cpf']);

        // Conditionally launch create table for auth_amei_blacklist.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Amei savepoint reached.
        upgrade_plugin_savepoint(true, 2025061200, 'auth', 'amei');
    }

    return true;
}
