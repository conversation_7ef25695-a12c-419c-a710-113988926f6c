<?php

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/authlib.php');
require_once($CFG->dirroot.'/login/lib.php');

use \auth_amei\config;
use \auth_amei\amei_oauth2_client;

class auth_plugin_amei extends auth_plugin_base {

    /**
     * Constructor.
     */
    public function __construct() {
        $this->authtype = config::AUTH_NAME;
        $this->config = get_config('auth_amei');
    }


    /**
     * Returns true if the username and password work or don't exist and false
     * if the user exists and the password is wrong.
     *
     * @param string $username The username
     * @param string $password The password
     * @return bool Authentication success or failure.
     */
    public function user_login($username, $password) {
        return false;
    }

    /**
     * Updates the user's password.
     *
     * called when the user password is updated.
     *
     * @param  object  $user        User table object
     * @param  string  $newpassword Plaintext password
     * @return boolean result
     *
     */
    public function user_update_password($user, $newpassword) {
        return false;
    }

    public function prevent_local_passwords() {
        return true;
    }

    /**
     * Returns true if this authentication plugin is 'internal'.
     *
     * @return bool
     */
    public function is_internal() {
        return false;
    }

    /**
     * Returns true if this authentication plugin can change the user's
     * password.
     *
     * @return bool
     */
    public function can_change_password() {
        return false;
    }

    /**
     * Returns the URL for changing the user's pw, or empty if the default can
     * be used.
     *
     * @return moodle_url
     */
    public function change_password_url() {
        return null;
    }

    /**
     * Returns true if plugin allows resetting of internal password.
     *
     * @return bool
     */
    public function can_reset_password() {
        return false;
    }

    /**
     * Returns true if plugin can be manually set.
     *
     * @return bool
     */
    public function can_be_manually_set() {
        return true;
    }


    /**
     * Will check if we have to redirect before going to login page
     */
    public function pre_loginpage_hook() {
        try {
            if ($this->should_login_redirect()) {
                $this->oauth2_complete_login_or_redirect();
            }

        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER);
        }
    }

    protected function get_oauth2_client() : amei_oauth2_client {
        return amei_oauth2_client::factory();
    }

    protected function oauth2_complete_login_or_redirect(){
        $client = $this->get_oauth2_client();
                
        if (!$client->is_logged_in()) {
            redirect($client->get_login_url());
        }

        $this->oauth2_complete_login($client);
    }

    protected function trigger_user_login_failed_event($failurereason = AUTH_LOGIN_NOUSER, ?object $user = null){
        if(empty($user)){
            $user = new \stdClass();
        }

        if(empty($user->username)){
            $user->username = 'unknown';
        }

        $data = [
            'other' => [
                'username' => $user->username,
                'reason' => $failurereason,
                'auth' => config::AUTH_NAME,
            ]
        ];

        if(!empty($user->id)){
            $data['userid'] = $user->id;
        }

        $event = \core\event\user_login_failed::create($data);
        $event->trigger();
    }

    /**
     * If the user is blacklisted, he should be redirected
     * and the login workflow interrupted.
     *
     * This method will call redirect(), which terminates script execution.
     *
     * @param string $username CPF used as username.
     * @return void
     */
    protected function check_black_list(string $username): void {
        global $DB;

        $record = $DB->get_record('auth_amei_blacklist', ['cpf' => $username]);
        if (empty($record)) {
            return;
        }

        $default_redirect_url = new moodle_url('/login/index.php');
        $redirect_url = empty($record->redirect_url) ? $default_redirect_url : $record->redirect_url;
        redirect($redirect_url);
    }

    protected function oauth2_complete_login(amei_oauth2_client $client){
        global $DB, $SESSION;

        $fail_url = new moodle_url('/login/index.php');
        // $fail_url = $client->get_logout_url();

        if(!config::is_enabled()){
            $client->log_out();
            redirect($fail_url);
        }

        if (!$client->is_logged_in()) {
            redirect($fail_url);
        }

        if(!$userinfo = $client->get_userinfo()){
            $this->trigger_user_login_failed_event(AUTH_LOGIN_NOUSER);

            $SESSION->loginerrormsg = get_string('loginerror:nouserinfo', 'auth_amei');
            $client->log_out();
            redirect($fail_url);
        }

        if(empty($userinfo->username)){
            $this->trigger_user_login_failed_event(AUTH_LOGIN_NOUSER);

            $SESSION->loginerrormsg = get_string('loginerror:malformeduser', 'auth_amei');
            $client->log_out();
            redirect($fail_url);
        }

        $this->check_black_list($userinfo->username);

        $conditions = ['username' => $userinfo->username];
        $userid = $DB->get_field('user', 'id', $conditions);
        if (!$user = get_complete_user_data('id', $userid)) {
            $this->trigger_user_login_failed_event(AUTH_LOGIN_NOUSER, (object) $conditions);

            $SESSION->loginerrormsg = get_string('loginerror:usernotfound', 'auth_amei');
            $client->log_out();
            redirect($fail_url);
        }

        if($user->suspended){
            $this->trigger_user_login_failed_event(AUTH_LOGIN_SUSPENDED, $user);

            $SESSION->loginerrormsg = get_string('invalidlogin');
            $client->log_out();
            redirect(new moodle_url('/login/index.php'));
        }

        // if(!$user->confirmed){
        //     /** @todo */
        // }

        complete_user_login($user);
        \core\session\manager::apply_concurrent_login_limit($user->id, session_id());

        $this->mark_as_logged_by_amei();

        $client::store_wantsurl();
        $url_to_go = core_login_get_return_url();
        unset($SESSION->wantsurl);

        redirect($url_to_go);
    }

    public function complete_login() {
        $client = $this->get_oauth2_client();
        $this->oauth2_complete_login($client);
    }

    /**
     * Determines if we will redirect to the redirect uri.
     *
     * @return bool
     */
    public function should_login_redirect() : bool {
        if ($noredirect = optional_param('noredirect', false, PARAM_BOOL)) {
            return false;
        }

        // Only redirects on GET requests.
        if (empty($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] != 'GET') {
            return false;
        }

        if(!config::is_enabled()){
            return false;
        }

        return true;
    }

    protected function mark_as_logged_by_amei(){
        global $SESSION;
        $SESSION->logged_by_amei = true;
    }

    protected function was_logged_by_amei() : bool {
        global $SESSION;
        return !empty($SESSION->logged_by_amei);
    }

    /**
     * Hook for overriding behaviour of logout page.
     * This method is called from login/logout.php page for all enabled auth plugins.
     *
     * @global object
     * @global string
     */
    function logoutpage_hook() {
        global $USER;
        global $redirect;

        if($USER->auth = 'amei' || $this->was_logged_by_amei()){
            $client = $this->get_oauth2_client();
            $client->log_out();

            $redirect = $client->get_logout_url()->out(false);
        }
    }

    /**
     * Post logout hook.
     *
     * This method is used after moodle logout by auth classes to execute server logout.
     *
     * @param stdClass $user clone of USER object before the user session was terminated
     */
    // public function postlogout_hook($user) {
    // }
}


